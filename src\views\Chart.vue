<template>
    <div style="width: 100%;padding: 20px">
        <div id="plot"></div>
        <div class="toolbar">
            <Button type="primary" @click="openModal = true">添加折线</Button>
            <br><br>
            <Button type="primary" @click="popChartData">清除折线</Button>
        </div>
        <Modal v-model="openModal" title="添加折线" width="600" @on-ok="addLine">
            <chart-modal ref="modal"></chart-modal>
        </Modal>
    </div>
</template>

<script>
    // 引入 ECharts 主模块
    var echarts = require('echarts');
    import ChartModal from './ChartModal'

    export default {
        name: "chart",
        components: {ChartModal},
        mounted() {
            this.initPlot()
        },
        data() {
            return {
                chart: null,
                openModal: false,
                length: 10,
                option: {
                    title: {
                        text: '折线图'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>x：{b}<br/>y：{c}'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
                    },
                    yAxis: {
                        type: 'value'
                    },
                    legend: {
                        left: 'center',
                        data: []
                    }
                }
            }
        },
        computed: {},
        methods: {
            initPlot: function () {
                this.chart = echarts.init(document.getElementById("plot"))
                this.chart.setOption(this.option)
            },
            pushChartData(data) {
                let series = this.chart.getOption().series
                if (series === undefined || !Array.isArray(series)) {
                    series = []
                }
                series.push({
                    name: data.name,
                    type: 'line',
                    data: data.list,
                    smooth: true,
                })
                this.updateOption(series)
            },
            popChartData() {
                let series = this.chart.getOption().series
                if (Array.isArray(series) && series.length > 0) {
                    series.pop()
                }
                this.updateOption(series, true)
            },
            updateOption(series, clear = false) {
                this.option.series = series
                this.option.xAxis.data = this.genLength(Math.max.apply(Math, series.map(s => s.data.length)))
                this.option.legend.data = series.map(s => s.name)
                this.chart.setOption(this.option, clear)
            },
            addLine() {
                this.pushChartData({
                    name: this.$refs.modal.formItem.name,
                    list: this.$refs.modal.formItem.data.trim().split(",").map(l => l.trim())
                })
            },
            genLength(length) {
                let data = []
                for (let i = 0; i < length; i++) {
                    data.push(i)
                }
                return data
            },
        }
    }
</script>

<style scoped>
    #plot {
        height: 500px;
    }

    .toolbar {
        position: absolute;
        bottom: 10px;
        right: 10px;
    }
</style>