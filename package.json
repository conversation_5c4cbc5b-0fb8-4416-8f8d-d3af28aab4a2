{"name": "locationmap", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve-inner": "vue-cli-service serve --hot --mode inner", "serve-yun": "vue-cli-service serve --hot --mode yun", "build": "vue-cli-service build", "build-inner": "vue-cli-service build --mode inner", "build-yun": "vue-cli-service build --mode yun", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.0", "core-js": "^3.3.2", "echarts": "^4.8.0", "file-saver": "^2.0.2", "less": "^3.13.1", "less-loader": "^5.0.0", "moment": "^2.27.0", "v-charts": "^1.19.0", "view-design": "^4.0.2", "vue": "^2.6.10", "vue-axios": "^2.1.5", "vue-clipboard2": "^0.3.1", "vue-codemirror": "^4.0.6", "vue-router": "^3.1.3", "vue-socket.io": "^3.0.7", "vuex": "^3.1.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-plugin-router": "^4.0.5", "@vue/cli-plugin-vuex": "^4.0.5", "@vue/cli-service": "^4.0.0", "babel-eslint": "^10.0.3", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"AMap": true, "Loca": true, "AMapUI": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"vue/html-self-closing": "off", "vue/no-parsing-error": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}