import Axios from 'axios';
export function searchPoi(word, city) {
  return new Promise((resolve, reject) => {
    const defaultCity = city || '110100';
    // http://192.168.2.74  http://10.255.76.47:7103
    Axios({
      url: `/poiSearch/SearchWebProject/PoiSearch`,
      method: 'get',
      headers: {
        key: 'ad6609c5afe3741b',
        Version: '1.0.0',
        ReqNo: '1111',
        'content-Type': 'application/xml',
      },
      responseType: 'blob',
      params: {
        data_type: 'POI',
        query_type: 'TQUERY',
        protocol: 'json',
        city: defaultCity,
        keywords: word,
        page_num: 10,
        page: 1,
        // citySuggestion: true,
        qii: true,
        key: 'ad6609c5afe3741b',
      },
    })
      .then((res) => {
        let reader = new FileReader();
        reader.readAsText(res.data, 'GBK');
        reader.onload = function() {
          const resData = JSON.parse(reader.result);
          resolve(resData.poi || []);
        };
      })
      .catch((err) => {
        reject(err);
      });
  });
}
