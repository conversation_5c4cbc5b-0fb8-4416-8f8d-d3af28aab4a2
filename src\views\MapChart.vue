<template>
    <div style="width: 100%;padding: 12px">
        <div style="margin-top: 12px;margin-left: 18px;margin-bottom: 12px">
            <label style="font-size: 18px">日期：</label>
            <DatePicker type="daterange" split-panels placeholder="选择日期范围"
                        :value="defaultRange" @on-change="initChar"></DatePicker>
        </div>
        <Row>
            <Col span="12">
                <div id="pv" class="plot"></div>
            </Col>
            <Col span="12">
                <div id="uv" class="plot"></div>
            </Col>
        </Row>
        <Row>
            <Col span="12">
                <div id="ip" class="plot"></div>
            </Col>
            <Col span="12">
                <div id="spend" class="plot"></div>
            </Col>
        </Row>
    </div>
</template>

<script>
    import {get} from "../api"
    import moment from 'moment'
    // 引入 ECharts 主模块
    var echarts = require('echarts');

    export default {
        name: "map-chart",
        mounted() {
            this.initDefaultRange()
            this.initChar(this.defaultRange)
        },
        data() {
            return {
                defaultRange: [],
                pvChart: null,
                pvOption: {
                    title: {
                        text: 'PV'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: []
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                },
                uvChart: null,
                uvOption: {
                    title: {
                        text: 'UV'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: []
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                },
                ipChart: null,
                ipOption: {
                    title: {
                        text: 'IP'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: []
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                },
                spendChart: null,
                spendOption: {
                    title: {
                        text: '响应时间ms'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: []
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                }
            }
        },
        computed: {},
        methods: {
            initDefaultRange: function () {
                let now = moment(new Date())
                let end = now.format("YYYY-MM-DD")
                let start = now.subtract(7, 'days').format("YYYY-MM-DD")
                this.defaultRange = [start, end]
            },
            initChar: function (dateStr) {
                let params = "startDate=" + dateStr[0] + "&endDate=" + dateStr[1]
                this.pvChart = echarts.init(document.getElementById("pv"))
                this.initPV(params)
                this.uvChart = echarts.init(document.getElementById("uv"))
                this.initUV(params)
                this.ipChart = echarts.init(document.getElementById("ip"))
                this.initIP(params)
                this.spendChart = echarts.init(document.getElementById("spend"))
                this.initSpend(params)
            },
            initPV(params) {
                get("/weblog/pv?" + params).then(response => {
                    if (response.data.code === 1) {
                        let data = response.data.data.list;
                        this.pvOption.legend.data = this.distinct(data.map(s => s.desc))
                        this.pvOption.xAxis.data = data[0].day
                        this.pvOption.series = data.map(s => {
                            return {
                                name: s.desc,
                                type: 'line',
                                stack: '数量',
                                data: s.count,
                            }
                        })
                        this.pvChart.setOption(this.pvOption, true)
                    } else {
                        this.$Message.warning(response.data.msg);
                    }
                    this.closeSpin()
                })
            },
            initUV(params) {
                get("/weblog/uv?" + params).then(response => {
                    if (response.data.code === 1) {
                        let data = response.data.data.list;
                        this.uvOption.legend.data = this.distinct(data.map(s => s.desc))
                        this.uvOption.xAxis.data = data[0].day
                        this.uvOption.series = data.map(s => {
                            return {
                                name: s.desc,
                                type: 'line',
                                stack: '数量',
                                data: s.count,
                            }
                        })
                        this.uvChart.setOption(this.uvOption, true)
                    } else {
                        this.$Message.warning(response.data.msg);
                    }
                    this.closeSpin()
                })
            },
            initIP(params) {
                get("/weblog/ip?" + params).then(response => {
                    if (response.data.code === 1) {
                        let data = response.data.data.list;
                        this.ipOption.legend.data = this.distinct(data.map(s => s.desc))
                        this.ipOption.xAxis.data = data[0].day
                        this.ipOption.series = data.map(s => {
                            return {
                                name: s.desc,
                                type: 'line',
                                stack: '数量',
                                data: s.count,
                            }
                        })
                        this.ipChart.setOption(this.ipOption, true)
                    } else {
                        this.$Message.warning(response.data.msg);
                    }
                    this.closeSpin()
                })
            },
            initSpend(params) {
                get("/weblog/spend?" + params).then(response => {
                    if (response.data.code === 1) {
                        let data = response.data.data.list;
                        this.spendOption.legend.data = this.distinct(data.map(s => s.desc))
                        this.spendOption.xAxis.data = data[0].day
                        this.spendOption.series = data.map(s => {
                            return {
                                name: s.desc,
                                type: 'line',
                                stack: '耗时ms',
                                data: s.avgTime,
                            }
                        })
                        this.spendChart.setOption(this.spendOption, true)
                    } else {
                        this.$Message.warning(response.data.msg);
                    }
                    this.closeSpin()
                })
            },
            distinct(array) {
                return array.reduce(function (new_array, old_array_value) {
                    if (new_array.indexOf(old_array_value) === -1) new_array.push(old_array_value);
                    return new_array;
                }, []);
            },
            closeSpin() {
                this.$nextTick(() => {
                    this.$Spin.hide()
                })
            },
        }
    }
</script>

<style scoped>
    .plot {
        height: 270px;
        padding: 12px;
    }
</style>