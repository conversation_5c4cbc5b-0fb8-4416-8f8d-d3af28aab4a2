<template>
    <Modal
        v-model="showModal"
        title="免责声明"
        width="600"
        :mask-closable="false"
        :closable="false"
        @on-ok="closeModal"
        footer-hide
    >
        <div class="modal-content">
            <p class="modal-text" style="font-size: 16px;font-weight: bold">
                请仔细阅读以下免责声明，以确保您明白您使用此应用程序的权利和义务。
            </p>
            <p class="modal-text">
                1. 应用程序功能：我们的应用程序提供了基于高德地图的地理位置信息展示和导航功能。所有地图数据和导航信息仅供参考，我们不对其准确性或完整性作出任何承诺。
            </p>
            <p class="modal-text">
                2. 使用风险：地图和导航数据可能会受到各种因素（如交通、天气等）的影响，从而导致不准确或过时的信息。在使用地图导航功能时，请始终保持警惕，遵循交通规则，谨慎驾驶或步行。
            </p>
            <p class="modal-text">
                3. 责任限制：我们不对因使用本应用程序而导致的任何直接或间接损失承担责任，包括但不限于财产损失、人身伤害等。用户应自行承担使用本应用程序的一切风险和责任。
            </p>
            <p class="modal-text" style="color: red">
                4. 商业用途限制：本应用程序仅供个人学习交流，不得将本应用程序用于任何商业目的，包括但不限于销售、推广、广告等活动。
            </p>
            <p class="modal-text">
                5. 条款变更：我们保留随时修改免责声明的权利。请定期查阅此声明，以获取最新信息。
            </p>
            <p class="modal-text" style="font-size: 16px;font-weight: bold">
                请在使用本应用程序之前务必认真阅读并理解上述内容。如果您不同意本免责声明的任何部分，请勿继续使用本应用程序。
            </p>
        </div>
        <div class="modal-footer">
            <Button
                type="primary"
                :disabled="countdown > 0"
                @click="closeModal"
                long
                size="large"
            >
                {{ countdown > 0 ? `确认 (${countdown}s)` : "确认" }}
            </Button>
        </div>
    </Modal>
</template>

<script>
import {Button, Modal} from "view-design";

export default {
    components: {
        Modal,
        Button,
    },
    data() {
        return {
            showModal: true,
            countdown: 10,
        };
    },
    methods: {
        openModal() {
            this.showModal = true;
        },
        closeModal() {
            this.showModal = false;
            localStorage.setItem("disclaimerConfirmed", "true");
        },
        startCountdown() {
            const isConfirmed = localStorage.getItem("disclaimerConfirmed");
            if (!isConfirmed) {
                const timer = setInterval(() => {
                    if (this.countdown > 0) {
                        this.countdown--;
                    } else {
                        clearInterval(timer);
                    }
                }, 1000);
            } else {
                this.countdown = 0
            }
        },
    },
    mounted() {
        this.startCountdown();
    },
};
</script>

<style scoped>
.modal-content {
    text-align: justify;
    padding: 0 12px;
}

.modal-text {
    font-size: 14px;
    line-height: 1.6;
    margin: 10px 0;
}

.modal-footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
