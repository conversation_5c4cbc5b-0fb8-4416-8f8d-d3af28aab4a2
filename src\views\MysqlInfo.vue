<template>
    <Form :model="tableForm" :label-width="60" inline>
        <FormItem>
            <Input type="text" v-model="tableForm.ip" style="width: 110px"></Input>
        </FormItem>
        <FormItem>
            <Input type="text" v-model="tableForm.port" style="width: 48px"></Input>
        </FormItem>
        <FormItem>
            <Input type="text" v-model="tableForm.user" style="width: 42px"></Input>
        </FormItem>
        <FormItem>
            <Input type="password" v-model="tableForm.passwd" style="width: 80px"></Input>
        </FormItem>
        <FormItem>
            <Input type="text" v-model="tableForm.db" style="width: 120px"></Input>
        </FormItem>
    </Form>
</template>

<script>

    export default {
        name: "mysql-info",
        data() {
            return {
                tableForm: {
                    ip: '*************',
                    port: 3306,
                    user: 'root',
                    passwd: 'mastercom168',
                    db: 'upos_city_main',
                },
            }
        },
        methods: {}
    }
</script>

<style scoped>

</style>