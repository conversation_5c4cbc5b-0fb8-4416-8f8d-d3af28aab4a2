import axios from "../axios";

export function post(url, datas, contentType = "multipart/form-data") {
  return axios({
    url: url,
    method: "post",
    xhrFields: { withCredentials: true },
    crossDomain: true,
    headers: {
      "Content-Type": contentType, //设置请求头请求格式form
    },
    data: datas,
  });
}

export function postJson(url, datas) {
  return post(url, datas, "application/json");
}

export function get(url) {
  return axios({
    url: url,
    method: "get",
  });
}

// 登录API
export function loginApi(username, password, captcha) {
  return postJson("/auth/login", {
    username,
    password,
    captcha,
  });
}

// 退出登录API
export function logoutApi() {
  return postJson("/auth/logout", {});
}

// 获取用户信息API
export function getUserInfoApi() {
  return get("/auth/userinfo");
}

// 获取验证码API
export function getCaptchaApi() {
  return get("/auth/captcha");
}
