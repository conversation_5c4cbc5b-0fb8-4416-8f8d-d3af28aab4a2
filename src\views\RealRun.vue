<template>
    <div class="outer">
        <div class="head">
            <Form ref="formData" :model="formData" :label-width="12" inline>
                <FormItem label="模板" :label-width="50">
                    <Select :value="codeModel" style="width:240px;text-align: left" filterable @on-change="onCodeChange"
                            ref="codeModelSelect">
                        <div slot="empty">暂无匹配数据</div>
                        <Option v-for="item in codeModelList" :value="item.value" :key="item.id" :label="item.label"/>
                    </Select>
                </FormItem>
                <FormItem>
                    <Button type="primary" :loading="codeRunning" @click="runCode" icon="md-play">
                        <span v-if="!codeRunning">运行代码</span>
                        <span v-else>正在执行</span>
                    </Button>
                </FormItem>
                <FormItem>
                    <Poptip width="400" title="保存模板" placement="bottom" transfer popper-class="inline-pop">
                        <Button type="primary" icon="md-cloud-download">保存模板</Button>
                        <div slot="content">
                            <Form ref="saveModelForm" :model="saveModelForm" inline :label-width="80">
                                <FormItem label="模版名称">
                                    <Input v-model="saveModelForm.modelName" placeholder="请输入模版名称"
                                           style="width: 200px"/>
                                </FormItem>
                                <FormItem :label-width="0">
                                    <Button type="primary" :loading="codeSaving" @click="saveModel">
                                        <span v-if="!codeSaving">保存</span>
                                        <span v-else></span>
                                    </Button>
                                </FormItem>
                            </Form>
                        </div>
                    </Poptip>
                </FormItem>
                <FormItem>
                    <Poptip width="400" title="更新模板" placement="bottom" transfer popper-class="inline-pop">
                        <Button type="primary" icon="md-cloud-upload">更新模板</Button>
                        <div slot="content">
                            <Form ref="updateModelForm" :model="updateModelForm" inline :label-width="80">
                                <FormItem label="模版名称">
                                    <Select v-model="updateModelForm.modelId" filterable transfer
                                            style="width: 200px">
                                        <Option v-for="item in codeModelList" :value="item.id" :key="item.id"
                                                :label="item.label"/>
                                    </Select>
                                </FormItem>
                                <FormItem :label-width="0">
                                    <Button type="primary" :loading="codeSaving" @click="updateModel">
                                        <span v-if="!codeSaving">更新</span>
                                        <span v-else></span>
                                    </Button>
                                </FormItem>
                            </Form>
                        </div>
                    </Poptip>
                </FormItem>
            </Form>
        </div>
        <div class="split">
            <Split v-model="split1">
                <div slot="left" class="split-pane" style="margin-bottom: 20px">
                    <codemirror ref="codeMirrorEditor" v-model="formData.code" :options="optionsCode"
                                @ready="onEditorReady" @changes="onEditorChange" class="code-edit">
                    </codemirror>
                </div>
                <div slot="right" class="split-pane">
                    <div class="code-log">
                        <codemirror ref="codeMirrorLog" :value="resultLog" :options="optionsLog" class="code-edit">
                        </codemirror>
                    </div>
                </div>
            </Split>
        </div>
    </div>
</template>

<script>
    import {get, post} from "../api";
    import {codemirror} from 'vue-codemirror'
    import "codemirror/theme/ambiance.css"
    // 主题
    import "codemirror/theme/blackboard.css";
    // java语法高亮
    require("codemirror/mode/clike/clike.js");
    require("codemirror/addon/edit/closebrackets.js");


    export default {
        name: "real-run",
        components: {
            codemirror
        },
        data() {
            return {
                split1: 0.5,
                formData: {
                    code: "public class HelloWorld {\n" +
                        "    public static void main(String[] args){\n" +
                        "        System.out.println(\"Hello World!\");\n" +
                        "    }\n" +
                        "}",
                },
                editor: null,
                codeRunning: false,
                codeSaving: false,
                codeModelList: [],
                codeModel: "",
                saveModelForm: {
                    modelName: "",
                },
                updateModelForm: {
                    modelId: "",
                },
                resultLog: "",
                optionsCode: {
                    mode: "text/x-java",  //Java语言
                    autofocus: true,
                    lineNumbers: true,   //显示行号
                    smartIndent: true, // 自动缩进
                    autoCloseBrackets: true,// 自动补全括号
                    indentUnit: 4,
                },
                optionsLog: {
                    readOnly: true,
                    autofocus: false,
                },
            }
        },
        mounted() {
            this.getModelList()
        },
        methods: {
            onEditorReady: function () {
                setTimeout(() => {
                    this.$refs.codeMirrorEditor.refresh()
                }, 50)
            },
            onCodeChange: function (value) {
                this.codeModel = value
                if (value) {
                    this.$refs.codeMirrorEditor.handerCodeChange(value)
                    this.$refs.codeMirrorEditor.refresh()
                }
            },
            onEditorChange: function (editor) {
                if (editor.getValue() !== this.codeModel) {
                    this.$refs.codeModelSelect.setQuery(null)
                }
            },
            runCode: function () {
                if (!this.codeRunning) {
                    this.codeRunning = true
                    post("/compiler/java/", this.formData)
                        .then(response => {
                            if (response.data.code === 1) {
                                this.resultLog = response.data.data.log
                            } else {
                                this.resultLog = response.data.msg
                            }
                            this.$Message.success("执行完成");
                            this.$nextTick(() => {
                                this.codeRunning = false
                            })
                        })
                }
            },
            saveModel: function () {
                if (!this.saveModelForm.modelName) {
                    this.$Message.warning("模板名称不能为空");
                    return
                }
                if (!this.codeSaving) {
                    this.codeSaving = true
                    let formData = new FormData();
                    formData.type = 'java'
                    formData.name = this.saveModelForm.modelName
                    formData.code = this.$refs.codeMirrorEditor.value
                    post("/compiler/model/save/", formData)
                        .then(response => {
                            if (response.data.code === 1) {
                                this.$Message.success("保存成功");
                            } else {
                                this.$Message.warning(response.data.msg);
                            }
                            this.$nextTick(() => {
                                this.codeSaving = false
                            })
                            this.getModelList()
                        })
                }
            },
            updateModel: function () {
                if (!this.updateModelForm.modelId) {
                    this.$Message.warning("模板名称不能为空");
                    return
                }
                if (!this.codeSaving) {
                    this.codeSaving = true
                    let formData = new FormData();
                    formData.id = this.updateModelForm.modelId
                    formData.code = this.$refs.codeMirrorEditor.value
                    post("/compiler/model/update/", formData)
                        .then(response => {
                            if (response.data.code === 1) {
                                this.$Message.success("更新成功");
                            } else {
                                this.$Message.warning(response.data.msg);
                            }
                            this.$nextTick(() => {
                                this.codeSaving = false
                            })
                            this.getModelList()
                        })
                }
            },
            getModelList: function () {
                get("/compiler/model/list/?type=java")
                    .then(response => {
                        if (response.data.code === 1) {
                            this.$nextTick(() => {
                                this.codeModelList = response.data.data.list.map(d => {
                                    return {
                                        id: d.id,
                                        label: d.name,
                                        value: d.code
                                    }
                                })
                            })
                        } else {
                            this.$Message.warning(response.data.msg);
                        }
                    })
            },
        }
    }
</script>

<style lang="less" scoped>
    .outer {
        position: relative;
        width: 100%;
        height: 100%;
        padding-top: 60px;

        .head {
            position: absolute;
            top: 0;
            padding: 13px;
        }

        .split {
            height: 100%;
            border: 1px solid #dcdee2;

            .split-pane {
                height: 100%;

                .code-edit {
                    height: 100%;
                }

                .code-log {
                    height: 100%;
                    overflow: auto;
                    margin-left: 8px;
                    padding: 8px;
                }
            }
        }
    }

    .top {
        z-index: 100000;
    }
</style>

<style lang="less">
    .head, .inline-pop {
        .ivu-form-item {
            margin-bottom: 0;
        }
    }

    .split-pane {
        .CodeMirror {
            height: 100%;
        }
    }
</style>