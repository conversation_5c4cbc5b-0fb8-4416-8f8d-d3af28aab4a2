import {bd09togcj02, bd09towgs84, gcj02tobd09, gcj02towgs84, wgs84tobd09, wgs84togcj02} from '../common/CoordConvert'

export function objToFormData(config) {
    //对象转formdata格式
    let formData = new FormData();
    let obj = config.data;
    let arrayKey = config.arrayKey;
    for (var i in obj) {
        if (Array.isArray(obj[i])) {
            obj[i].map(item => {
                if (!arrayKey) {
                    formData.append(i, item)
                } else {
                    formData.append(i + '[]', item)
                }
            })
        } else if (Object.prototype.toString.call(obj[i]) === '[object Object]') {
            formData.append(i, JSON.stringify(obj[i]))
        } else {
            formData.append(i, obj[i])
        }
    }
    return formData;
}

export function handleLocalStorage(method, key, value) {
    switch (method) {
        case 'get' : {
            let temp = window.localStorage.getItem(key);
            if (temp) {
                return temp
            } else {
                return false
            }
        }
        case 'set' : {
            window.localStorage.setItem(key, value);
            break
        }
        case 'remove': {
            window.localStorage.removeItem(key);
            break
        }
        default : {
            return false
        }
    }
}

export function randomSvg(color) {
    let title = readSvg(require('../assets/point.svg'))
    let newSvg = title.replace(/#(?:[0-9a-fA-F]{6})/g, color)
    return 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent(newSvg)));
}

export function readSvg(filePath) {
    // 创建一个新的xhr对象
    let xhr = null
    if (window.XMLHttpRequest) {
        xhr = new XMLHttpRequest()
    } else {
        return null
    }
    const okStatus = document.location.protocol === 'file' ? 0 : 200
    xhr.open('GET', filePath, false)
    xhr.overrideMimeType('image/svg+xml')
    xhr.send(null)
    return xhr.status === okStatus ? xhr.responseText : null
}

const pointRegex = /\d+(\.\d+)?/g
const lnglatRegex = /(\d+(\.\d+)?(\D)+\d+(\.\d+)?)/g

export const yunEnv = process.env.VUE_APP_ENV_VAR && process.env.VUE_APP_ENV_VAR === 'yun'
export const innerEnv = process.env.VUE_APP_ENV_VAR && process.env.VUE_APP_ENV_VAR === 'inner'
export const fullEnv = !(process.env.VUE_APP_ENV_VAR) || null

export function pointChange(pointList, sourceCoordinate, sourceFormat, resultCoordinate, resultFormat) {
    return pointList.map(line => {
        return replaceLnglatByRegex(line, sourceCoordinate, sourceFormat, resultCoordinate, resultFormat)
    }).join("\n")
}

export function polygonChange(polygonList, sourceCoordinate, sourceFormat, resultCoordinate, resultFormat, delimiter = ",", locationDelimiter = ";") {
    return polygonList.map(polygonStr => {
        let pointListAll = []
        if (sourceFormat === 'blob') {
            let pointList = []
            let str = polygonStr.substring(2, polygonStr.length)
            for (let i = 0; i < str.length; i += 16) {
                let longitude = hex2Int(str.substring(i, i + 8))
                let latitude = hex2Int(str.substring(i + 8, i + 16))
                let doubleLnglat = changePoint2Double(longitude, latitude)
                pointList.push(doubleLnglat)
            }
            pointListAll.push(pointList)
        } else if (sourceFormat === 'wkt') {
            let indexOf = polygonStr.indexOf("POLYGON");
            if (indexOf >= 0) {
                polygonStr.substring(indexOf + 7, polygonStr.length - 1).split(")),((")
                    .map(points => {
                        let pointList = []
                        points.replaceAll("(", "").replaceAll(")", "").split(",")
                            .map(point => {
                                let splits = point.trim().split(" ")
                                if (splits != null && splits.length > 1) {
                                    let doubleLnglat = changePoint2Double(splits[0].trim(), splits[1].trim(), sourceFormat)
                                    pointList.push(doubleLnglat)
                                }
                            })
                        pointListAll.push(pointList)
                    })
            }
        } else if (sourceFormat === 'regex') {
            // 正则替换
            return polygonStr.replaceAll(lnglatRegex, old => replaceLnglatByRegex(old, sourceCoordinate, sourceFormat, resultCoordinate, resultFormat))
        } else {
            let pointList = []
            let polygons = polygonStr.split(locationDelimiter);
            if (polygons != null && polygons.length > 1) {
                polygons.forEach(pointStr => {
                    let splits = pointStr.split(delimiter)
                    if (splits != null && splits.length > 1) {
                        let doubleLnglat = changePoint2Double(splits[0], splits[1], sourceFormat)
                        pointList.push(doubleLnglat)
                    }
                })
            }
            pointListAll.push(pointList)
        }
        if (pointListAll.length === 0) return ''
        if (resultFormat === 'blob') {
            let hex = '0x'
            pointListAll[0].forEach(doubleLnglat => {
                let coordinateLnglat = changeCoordinate(doubleLnglat[0], doubleLnglat[1], sourceCoordinate, resultCoordinate)
                let result = doublePoint2Format(coordinateLnglat[0], coordinateLnglat[1], "int")
                let longitudeHex = int2HexRevers(result[0])
                let latitudeHex = int2HexRevers(result[1])
                hex += longitudeHex + latitudeHex
            })
            return hex
        } else if (resultFormat === 'wkt') {
            let wktStr = pointListAll.map(p => {
                return p.map(a => {
                    let coordinateLnglat = changeCoordinate(a[0], a[1], sourceCoordinate, resultCoordinate)
                    let result = doublePoint2Format(coordinateLnglat[0], coordinateLnglat[1], resultFormat)
                    return result[0] + ' ' + result[1]
                }).join(",")
            }).join(")), ((")
            if (pointListAll.length > 1) {
                return 'MULTIPOLYGON (((' + wktStr + ')))'
            } else {
                return 'POLYGON ((' + wktStr + '))'
            }
        } else {
            let pointPolygon = ''
            pointListAll[0].forEach(doubleLnglat => {
                let coordinateLnglat = changeCoordinate(doubleLnglat[0], doubleLnglat[1], sourceCoordinate, resultCoordinate)
                let result = doublePoint2Format(coordinateLnglat[0], coordinateLnglat[1], resultFormat)
                pointPolygon += result[0] + delimiter + result[1] + locationDelimiter
            })
            return pointPolygon
        }
    }).join("\n")
}

function replaceLnglatByRegex(line, sourceCoordinate, sourceFormat, resultCoordinate, resultFormat) {
    let lnglat = line.match(pointRegex)
    if (lnglat != null && lnglat.length > 1) {
        let doubleLnglat = changePoint2Double(lnglat[0], lnglat[1], sourceFormat)
        let coordinateLnglat = changeCoordinate(doubleLnglat[0], doubleLnglat[1], sourceCoordinate, resultCoordinate)
        let result = doublePoint2Format(coordinateLnglat[0], coordinateLnglat[1], resultFormat)
        let i = 0
        return line.replaceAll(pointRegex, () => {
            // 经纬度，只处理前两个
            if (i >= 2) {
                return ''
            }
            return result[i++]
        })
    }
    return ''
}

function changePoint2Double(longitude, latitude, sourceFormat) {
    if (sourceFormat === "int" || sourceFormat === "blob") {
        return [Number(longitude / 10000000), Number(latitude / 10000000)]
    }
    return [parseFloat(longitude), parseFloat(latitude)]
}

function doublePoint2Format(longitude, latitude, resultFormat) {
    if (resultFormat === "int" || resultFormat === "blob") {
        return [Number(longitude * 10000000).toFixed(0), Number(latitude * 10000000).toFixed(0)]
    }
    return [longitude.toFixed(7), latitude.toFixed(7)]
}

function changeCoordinate(longitude, latitude, sourceCoordinate, resultCoordinate) {
    if (sourceCoordinate === resultCoordinate) {
        return [longitude, latitude]
    } else {
        let lnglat = [longitude, latitude];
        switch (sourceCoordinate + "_" + resultCoordinate) {
            case "WGS84_GCJ02":
                lnglat = wgs84togcj02(longitude, latitude)
                break;
            case "WGS84_BD09":
                lnglat = wgs84tobd09(longitude, latitude)
                break;
            case "BD09_WGS84":
                lnglat = bd09towgs84(longitude, latitude)
                break;
            case "BD09_GCJ02":
                lnglat = bd09togcj02(longitude, latitude)
                break;
            case "GCJ02_WGS84":
                lnglat = gcj02towgs84(longitude, latitude)
                break;
            case "GCJ02_BD09":
                lnglat = gcj02tobd09(longitude, latitude)
                break;
            default:
                break
        }
        return lnglat
    }
}

export function hex2Int(hexStr) {
    if (hexStr.length !== 8) {
        return 0
    }
    let int = 0
    for (let i = hexStr.length - 1; i > 0; i -= 2) {
        int += Number('0x' + hexStr.substring(i - 1, i + 1)) << ((i - 1) * 4)
    }
    return int
}

export function int2HexRevers(int) {
    int = parseInt(int)
    let hex = strPad(int.toString(16))
    let hexStr = ''
    for (let i = hex.length - 1; i > 0; i -= 2) {
        hexStr += hex.substring(i - 1, i + 1)
    }
    return hexStr.toUpperCase()
}

function strPad(hex) {
    let zero = '00000000';
    let tmp = 8 - hex.length;
    return zero.substr(0, tmp) + hex;
}

export function revertColor(color) {
    return color.substring(0, 1) + color.substring(1, color.length).split("").reverse().join("")
}


export function getPathDistance(lnglatPath) {
    let distance = 0;
    for (let i = 0; i < lnglatPath.length - 1; i++) {
        distance += getDistance(lnglatPath[i][0], lnglatPath[i][1], lnglatPath[i + 1][0], lnglatPath[i + 1][1])
    }
    return distance
}

export function getDistance(lng1, lat1, lng2, lat2) {
    lat1 = lat1 || 0;
    lng1 = lng1 || 0;
    lat2 = lat2 || 0;
    lng2 = lng2 || 0;

    const rad1 = lat1 * Math.PI / 180.0;
    const rad2 = lat2 * Math.PI / 180.0;
    const a = rad1 - rad2;
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    const r = 6378137;
    return r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
}
