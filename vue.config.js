module.exports = {
  configureWebpack: {
    externals: {
      AMap: "AMap",
      Loca: "Loca",
      AMapUI: "AMapUI",
    },
  },
  publicPath: "",
  css: {
    extract: true, // 是否使用css分离插件 ExtractTextPlugin
    sourceMap: false, // 开启 CSS source maps?
    loaderOptions: {
      less: {
        javascriptEnabled: true, //less 配置
      },
    }, // css预设器配置项
  },
  devServer: {
    port: "8080", // 设置端口号
    proxy: {
      "/poiSearch/": {
        target: "http://************:8101/", //API服务器的地址
        ws: true, //代理websockets
        changeOrigin: true, // 是否跨域，虚拟的站点需要更管origin
        pathRewrite: {
          "^/poiSearch": "",
        },
      },
      "/ali-datav/": {
        target: "https://geo.datav.aliyun.com/",
        changeOrigin: true,
        pathRewrite: {
          "^/ali-datav": "",
        },
      },
    },
  },
};
