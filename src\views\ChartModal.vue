<template>
    <div>
        <Form :model="formItem" :label-width="90" inline>
            <FormItem label="折线名称">
                <Input v-model="formItem.name" placeholder="折线名称" style="width: 400px"/>
            </FormItem>
            <br>
            <FormItem label="折线类型">
                <Select style="width: 400px" v-model="formItem.type">
                    <Option v-for="item in typeList" :value="item.value" :key="item.value" :label="item.label"></Option>
                </Select>
            </FormItem>
            <br>
            <FormItem label="折线数据">
                <Input v-model="formItem.data" type="textarea" :rows="6" placeholder="多个值用逗号隔开" style="width: 400px"/>
            </FormItem>
        </Form>
    </div>
</template>

<script>
    export default {
        name: "chart-modal",
        props: [],
        data: () => {
            return {
                filename: "",
                formItem: {
                    name: '',
                    type: 'line',
                    data: '',
                },
                typeList: [
                    {
                        label: "折线",
                        value: "line"
                    },
                ]
            }
        },
    }
</script>

<style scoped>

</style>