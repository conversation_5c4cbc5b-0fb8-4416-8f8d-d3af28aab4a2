stages:
  - install
  - build
  - deploy
  
cache:
  key: ${CI_BUILD_REF_NAME}
  paths:
    - node_modules/
    - dist/

install:
  only:
    - master
  stage: install
  tags:
    - test
  script:
    - npm install

build:
  only:
    - master
  stage: build
  tags:
    - test
  script:
    - npm run build
  
deploy:
  only:
    - master
  stage: deploy
  tags:
    - test
  script:
    - ssh hmaster@hadoop-dn01 "rm -rf /home/<USER>/tool/dist_bak;mv /home/<USER>/tool/dist /home/<USER>/tool/dist_bak"
    - scp -r dist/ hmaster@hadoop-dn01:/home/<USER>/tool/
