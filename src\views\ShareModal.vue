<template xmlns="http://www.w3.org/1999/html">
    <div>
        <Form :model="formItem" ref="feedbackForm" :label-width="100">
            <FormItem label="有效时间" prop="expired">
                <RadioGroup v-model="formItem.expired">
                    <Radio :label="30">30分钟</Radio>
                    <Radio :label="-1" disabled>永久</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="注意事项">
                <p>分享后的链接附带经纬度坐标信息，所有获得此链接的用户都可以查看，请注意数据的安全性</p>
            </FormItem>
            <FormItem label="分享链接" prop="url">
                <Input disabled v-model="formItem.url" style="width: 300px"/>
            </FormItem>
            <FormItem>
                <p v-show="getKeys.length > keyLimit" style="font-size: 16px">当前打点图层数量超过<span class="red-big"> {{ keyLimit }} </span>次，
                    将只分享最近<span class="red-big"> {{ keyLimit }} </span>次打点信息</p>
                <Button type="primary" @click="createShare" v-show="!this.formItem.url"
                        :disabled="getKeys.length === 0">创建链接
                </Button>
                <Button type="primary" v-clipboard="this.formItem.url" v-clipboard:success="copySuccess"
                        v-show="this.formItem.url">复制链接
                </Button>
            </FormItem>
        </Form>
    </div>
</template>

<script>
import {post} from "@/api";

export default {
    name: "share-modal",
    data() {
        return {
            keyLimit: 10,
            formItem: {
                expired: 30,
                url: '',
            },
        }
    },
    props: {
        keyDic: {
            type: Object
        },
    },
    computed: {
        getKeys() {
            let allKeys = []
            for (const key in this.keyDic) {
                if (this.keyDic.hasOwnProperty(key)) {
                    allKeys.push(this.keyDic[key])
                }
            }
            return allKeys
        },
    },
    methods: {
        createShareByText() {
            let mapModal = this.$parent.$parent.$refs.modal;
            let formData = mapModal.formItem;
            formData.file = new Blob([mapModal.fileText], {type: "text/html;charset=utf-8"});
            post("/shared/create", formData).then(response => {
                let data = response.data.data;
                if (response.data.code === 1) {
                    this.buildUrl(data.key)
                }
            })
        },
        createShare() {
            let formData = new FormData();
            formData.keys = this.getKeys.slice(-this.keyLimit)
            post("/shared/create", formData).then(response => {
                let data = response.data.data;
                if (response.data.code === 1) {
                    this.buildUrl(data.key)
                }
            })
        },
        buildUrl(key) {
            let href = window.location.href
            let oldKey = this.$route.params.key;
            if (oldKey && oldKey !== '') {
                this.formItem.url = href.replace(oldKey, key)
            } else {
                this.formItem.url = href + '/' + key
            }
        },
        copySuccess() {
            this.$Message.info("链接复制成功");
        }
    }
}
</script>

<style scoped>
.red-big {
    font-size: 24px;
    color: red;
}
</style>