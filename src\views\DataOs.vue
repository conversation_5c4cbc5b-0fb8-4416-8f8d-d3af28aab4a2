<template>
    <div>
        <Card style="padding-top: 12px">
            <Form :model="tableForm" ref="tableForm" :label-width="120" inline
                  @keydown.native.enter.prevent="searchModel(0)">
                <FormItem label="离线调度名称">
                    <Input type="text" v-model="tableForm.name" style="width: 220px"></Input>
                </FormItem>
                <FormItem>
                    <Button type="primary" @click="searchModel(0)">查询</Button>
                </FormItem>
            </Form>
        </Card>
        <div>
            <Table no-data-text="无查询结果" border :loading="loading" :columns="columns" :data="tableData">
                <template #action="{ row, index }">
                    <Button type="primary" size="small" :loading="modelStatusObj.hasOwnProperty(row.id)"
                            v-if="modelStatusObj.hasOwnProperty(row.id)">
                        处理中...
                    </Button>
                    <template v-else>
                        <Button type="primary" size="small" @click="lineModel(row, online)" style="margin-right: 12px">一键上线</Button>
                        <Button type="error" size="small" @click="lineModel(row, offline)">一键下线</Button>
                    </template>
                </template>
            </Table>
            <Page :total="tableCount" style="padding: 12px"
                  show-sizer
                  :page-size-opts="[10,20,40,80]"
                  :page-size="pageSize"
                  @on-change="changePageNum"
                  @on-page-size-change="changePageSize"
                  @on-prev="changePageNum"
                  @on-next="changePageNum"/>
        </div>
    </div>
</template>

<script>
import {get, postJson} from "@/api";

export default {
    name: "data-os",
    data() {
        return {
            online: 1,
            offline: 2,
            tableForm: {
                name: '',
            },
            modelStatusObj: {},
            tableData: [],
            tableCount: 0,
            pageSize: 10,
            pageNum: 0,
            columns: [
                {
                    key: "id",
                    title: "ID",
                    width: 180,
                },
                {
                    key: "name",
                    title: "名称",
                    width: 360,
                },
                {
                    key: "label",
                    title: "中文名"
                }, {
                    key: "procCycle",
                    title: "周期",
                    width: 65,
                }, {
                    key: "procScriptType",
                    title: "类型",
                    width: 65,
                }, {
                    key: "procScriptServer",
                    title: "中心服务器",
                    width: 200,
                }, {
                    key: "state",
                    title: "状态",
                    width: 65,
                },
                {
                    title: '操作',
                    slot: 'action',
                    width: 200,
                    align: 'center'
                }
            ],
            loading: false,
        }
    },
    mounted() {
        this.searchModel()
    },
    computed: {},
    methods: {
        changePageSize(pageSize) {
            this.pageSize = pageSize
            this.searchModel()
        },
        changePageNum(page) {
            this.pageNum = (page || 1) - 1
            this.searchModel()
        },
        searchModel(pageNum = this.pageNum) {
            if (!this.loading) {
                this.loading = true;
                this.tableData = []
                this.pageNum = pageNum
                get(`/dataos/model/search?name=${this.tableForm.name}&pageNum=${this.pageNum}&pageSize=${this.pageSize}`)
                    .then(response => {
                        if (response.data.success) {
                            let data = response.data.data
                            let models = data.content
                            this.tableCount = data.totalElements
                            models.map(model => {
                                this.tableData.push({
                                    id: model.id,
                                    name: model.name,
                                    label: model.label,
                                    procCycle: this.cycleName(model.procCycle),
                                    procScriptType: model.procScriptType,
                                    procScriptServer: model.procScriptServer,
                                    state: this.stateName(model.state),
                                })
                            });
                        }
                        this.loading = false
                    })
            } else {
                this.$Message.warning("操作太频繁了!");
            }
        },
        lineModel(row, type) {
            let json = JSON.stringify({
                id: row.id,
                name: row.name,
                type: type
            })
            this.$set(this.modelStatusObj, row.id, true);
            postJson("/dataos/model/operate", json).then(response => {
                if (response.data.success) {
                    this.$Message.success("操作成功！编号：" + response.data.data.flowId);
                    this.searchModel()
                }
                this.$delete(this.modelStatusObj, row.id);
            })
        },
        cycleName(value) {
            switch (value) {
                case "minute":
                    return "分钟";
                case "hour":
                    return "小时";
                case "day":
                    return "天";
                case "month":
                    return "月";
                case "year":
                    return "年";
                default:
                    return "未知";
            }
        },
        stateName(value) {
            switch (value) {
                case "NEW":
                    return "新建";
                case "CHANGE":
                    return "变更";
                case "VALID":
                    return "生效";
                case "INVALID":
                    return "失效";
                default:
                    return "未知";
            }
        }
    }
}
</script>

<style scoped>

</style>