<template>
  <div class="shape-drawer">
    <div class="shape-drawer-header">
      <div class="shape-drawer-tip">
        <Icon type="md-information-circle" size="16" color="#2d8cf0" />
        <span>点击左侧地图选择行政区划范围</span>
      </div>
    </div>

    <div class="shape-drawer-current">
      <Card :bordered="false" dis-hover>
        <p slot="title" class="card-title">
          <Icon type="md-pin" />
          当前选择
        </p>
        <div class="current-content">
          <div class="current-item">
            <span class="label">地名:</span>
            <span class="value">{{ selectedName }}</span>
          </div>
          <div class="current-item">
            <span class="label">adcode:</span>
            <span class="value">{{ selectedAdcode }}</span>
          </div>
        </div>
      </Card>
    </div>

    <div class="shape-drawer-granularity">
      <Card :bordered="false" dis-hover>
        <p slot="title" class="card-title">
          <Icon type="md-options" />
          数据粒度
        </p>
        <div class="granularity-options">
          <RadioGroup
            v-model="granularity"
            @on-change="handleGranularityChange"
            type="button"
          >
            <Radio
              v-if="availableGranularities.includes('self')"
              label="self"
              >{{ currentLevelName }}</Radio
            >
            <Radio
              v-if="availableGranularities.includes('province')"
              label="province"
              >省(自治区、直辖市)</Radio
            >
            <Radio v-if="availableGranularities.includes('city')" label="city"
              >市</Radio
            >
            <Radio
              v-if="availableGranularities.includes('district')"
              label="district"
              >区(县)</Radio
            >
          </RadioGroup>
        </div>
      </Card>
    </div>

    <div class="shape-drawer-api">
      <Card :bordered="false" dis-hover>
        <p slot="title" class="card-title">
          <Icon type="md-code" />
          JSON API
        </p>
        <div class="api-content">
          <div class="api-url">
            <Input v-model="toCopyUrls.jsonUrl" readonly>
              <Button
                slot="append"
                @click="copyToClipboard(toCopyUrls.jsonUrl)"
                title="复制"
              >
                <Icon type="md-copy" />
              </Button>
            </Input>
          </div>
          <Divider size="small">或</Divider>
          <div class="api-url">
            <Input v-model="toCopyUrls.geojsonUrl" readonly>
              <Button
                slot="append"
                @click="copyToClipboard(toCopyUrls.geojsonUrl)"
                title="复制"
              >
                <Icon type="md-copy" />
              </Button>
            </Input>
          </div>
        </div>
      </Card>
    </div>

    <div class="shape-drawer-upload">
      <Card :bordered="false" dis-hover>
        <p slot="title" class="card-title">
          <Icon type="md-cloud-upload" />
          上传轮廓JSON
        </p>
        <div class="upload-content">
          <Upload
            action=""
            :before-upload="handleFileUpload"
            accept=".json,.geojson"
            :show-upload-list="false"
          >
            <Button icon="md-cloud-upload" type="primary" long
              >选择文件上传</Button
            >
          </Upload>
          <div class="file-info" v-if="uploadedFileName">
            <span>已选择: {{ uploadedFileName }}</span>
            <Button type="text" @click="clearUploadedFile" size="small">
              <Icon type="md-close" />
            </Button>
          </div>
        </div>
      </Card>
    </div>

    <div class="shape-drawer-color">
      <Card :bordered="false" dis-hover>
        <p slot="title" class="card-title">
          <Icon type="md-color-palette" />
          轮廓颜色设置
        </p>
        <div class="color-content">
          <div class="color-picker-wrapper">
            <span class="color-label">边框颜色:</span>
            <ColorPicker
              v-model="strokeColor"
              size="large"
              @on-change="emitColorUpdate"
            />
            <span class="color-value">{{ strokeColor }}</span>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script>
import {
  hexToRgba,
  isMunicipality,
  getAvailableGranularities,
} from "../utils/mapUtils";

export default {
  name: "ShapeDrawer",
  props: {
    // 接收父组件传递的JSON API URL
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectedName: "",
      selectedAdcode: "",
      allLevels: ["country", "province", "city", "district"], // 所有级别由大到小
      currentLevel: "", // country, province, city, district
      granularity: "self",
      regionOptions: [],
      allRegions: [],
      availableGranularities: ["self"],
      strokeColor: "#1890ff", // 默认边框颜色
      uploadedFileName: "", // 上传的文件名
      uploadedFileContent: null, // 上传的文件内容
      urls: {
        jsonUrl: "",
        geojsonUrl: "",
      },
    };
  },
  computed: {
    currentLevelName() {
      const levelMap = {
        country: "国家",
        province: "省(自治区、直辖市)",
        city: "市",
        district: "区(县)",
      };
      return levelMap[this.currentLevel] || "未知";
    },
    // 计算填充颜色（边框颜色的0.05透明度）
    fillColor() {
      return hexToRgba(this.strokeColor, 0.05);
    },
    toCopyUrls() {
      const datavPrefix = "https://geo.datav.aliyun.com";
      return {
        jsonUrl: datavPrefix + this.urls.jsonUrl.replace("/ali-datav", ""),
        geojsonUrl:
          datavPrefix + this.urls.geojsonUrl.replace("/ali-datav", ""),
      };
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.init();
      }
    },
    granularity(newVal) {
      if (!this.availableGranularities.includes(newVal)) {
        this.granularity = "self";
      }
    },
    // 监听URL变化，通知父组件
    urls: {
      handler() {
        this.emitUrlsUpdate();
      },
      deep: true,
    },
    // 监听颜色变化
    strokeColor() {
      this.emitColorUpdate();
    },
  },
  methods: {
    init() {
      // 从接口获取数据
      this.fetchAreasData();
    },
    // 从接口获取行政区数据
    fetchAreasData() {
      fetch("/ali-datav/areas_v3/bound/all.json", {
        referrerPolicy: "no-referrer",
      })
        .then((response) => response.json())
        .then((data) => {
          // 处理接口返回的数据
          this.allRegions = (data || [])
            .filter((item) => item && item.adcode) // 过滤无效项（adcode 为假值的项）
            .map(({ adcode, name, level, parent }) => ({
              adcode: adcode.toString(),
              name,
              level,
              parent: parent ? parent.toString() : null,
            }));

          // 选择第一个行政区
          this.selectFirstRegion();
        })
        .catch(() => {});
    },

    // 选择第一个行政区（通常是中国）
    selectFirstRegion() {
      if (this.allRegions && this.allRegions.length > 0) {
        // 选择第一个行政区
        const firstRegion = this.allRegions[0];
        this.setCurrentRegion(firstRegion);
      }
    },
    setCurrentRegion(region) {
      this.selectedName = region.name;
      this.selectedAdcode = region.adcode;
      this.currentLevel = region.level;

      this.updateAvailableGranularities(region.childrenNum === 0);

      this.$nextTick(() => {
        this.updateUrls();
      });
    },
    // 更新可用的粒度选项
    updateAvailableGranularities(noChild = true) {
      this.availableGranularities = getAvailableGranularities(
        this.currentLevel,
        this.selectedAdcode,
        noChild
      );
    },

    // 计算JSON API URL
    updateUrls() {
      const code = this.selectedAdcode;
      const suffix = this.getUrlSuffix();
      this.urls = {
        jsonUrl: `/ali-datav/areas_v3/bound/${code}${suffix}.json`,
        geojsonUrl: `/ali-datav/areas_v3/bound/geojson?code=${code}${suffix}`,
      };
    },
    handleGranularityChange() {
      this.updateUrls();
    },
    // 获取URL后缀
    getUrlSuffix() {
      if (!this.selectedAdcode) return "";

      // 确定粒度位置
      let granularityPosition = 0;

      // 根据当前级别确定粒度位置
      if (this.currentLevel === "country") {
        if (this.granularity === "self") granularityPosition = 1;
        else if (this.granularity === "province") granularityPosition = 2;
        else if (this.granularity === "city") granularityPosition = 3;
      } else if (this.currentLevel === "province") {
        if (this.granularity === "self") granularityPosition = 1;
        else if (this.granularity === "city") granularityPosition = 2;
        else if (this.granularity === "district") granularityPosition = 3;
      } else if (this.currentLevel === "city") {
        if (this.granularity === "self") granularityPosition = 1;
        else if (this.granularity === "district") granularityPosition = 2;
      } else if (this.currentLevel === "district") {
        if (this.granularity === "self") granularityPosition = 1;
      }
      // 直辖市
      if (isMunicipality(this.selectedAdcode)) {
        if (this.granularity === "self") granularityPosition = 1;
        else if (this.granularity === "district") granularityPosition = 2;
      }

      // 根据粒度位置确定后缀
      if (granularityPosition === 1) {
        // 第一粒度无后缀
        return "";
      } else if (granularityPosition === 2) {
        // 第二粒度统一为_full
        return "_full";
      } else if (granularityPosition === 3) {
        // 第三粒度根据当前级别确定
        if (this.currentLevel === "country") {
          return "_full_city"; // 国家级别下的第三粒度
        } else {
          return "_full_district"; // 其他级别下的第三粒度
        }
      }

      return "";
    },
    // 向父组件发送URL更新事件
    emitUrlsUpdate() {
      this.$emit("urls-update", {
        jsonUrl: this.urls.jsonUrl,
        geojsonUrl: this.urls.geojsonUrl,
        suffix: this.getUrlSuffix(),
        // 添加区域和粒度信息，但不包含颜色和文件数据
        region: {
          name: this.selectedName,
          adcode: this.selectedAdcode,
          level: this.currentLevel,
        },
        granularity: this.granularity,
      });
    },
    // 向父组件发送颜色更新事件
    emitColorUpdate() {
      // 发送单独的颜色更新事件
      this.$emit("color-update", {
        strokeColor: this.strokeColor,
        fillColor: this.fillColor,
      });
    },
    copyToClipboard(text) {
      const el = document.createElement("textarea");
      el.value = text;
      document.body.appendChild(el);
      el.select();
      document.execCommand("copy");
      document.body.removeChild(el);
      this.$Message.success("复制成功");
    },
    // 处理文件上传
    handleFileUpload(file) {
      // 保存文件名
      this.uploadedFileName = file.name;

      // 读取文件内容
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          // 解析JSON
          const jsonContent = JSON.parse(e.target.result);
          this.uploadedFileContent = jsonContent;

          // 从JSON内容中提取信息
          let regionName = "";
          let regionAdcode = "";
          let regionLevel = ""; // 默认为自定义级别
          let childrenNum = 0;

          // 尝试从不同格式的GeoJSON中提取名称和代码
          if (jsonContent.features && jsonContent.features.length > 0) {
            const feature = jsonContent.features[0];
            const properties = feature.properties || {};

            if (jsonContent.features.length === 1) {
              // 单条数据处理
              regionName = properties.name;
              regionAdcode = properties.adcode && properties.adcode.toString();
              regionLevel = properties.level;
              childrenNum = properties.childrenNum;
            } else {
              // 多条数据处理（有内轮廓）
              const parentAdcode =
                properties.parent &&
                properties.parent.adcode &&
                properties.parent.adcode.toString();
              const parentRegion = this.allRegions.find(
                (item) => item.adcode === parentAdcode
              );
              if (parentRegion) {
                regionName = parentRegion.name;
                regionAdcode = parentRegion.adcode;
                regionLevel = parentRegion.level;
                childrenNum = parentRegion.childrenNum;
              } else {
                regionName = regionAdcode = regionLevel = "";
              }
            }
          } else if (
            jsonContent.geometries &&
            jsonContent.geometries.length > 0
          ) {
            const geometry = jsonContent.geometries[0];
            const properties = geometry.properties || {};

            if (jsonContent.geometries.length === 1) {
              // 单条数据处理（阿里云格式）
              regionName = properties.name;
              regionAdcode = properties.adcode && properties.adcode.toString();
              regionLevel = properties.level;
              childrenNum = properties.childrenNum;
            } else {
              // 多条数据处理（有内轮廓）
              const parentAdcode =
                properties.parent &&
                properties.parent.adcode &&
                properties.parent.adcode.toString();
              const parentRegion = this.allRegions.find(
                (item) => item.adcode === parentAdcode
              );
              if (parentRegion) {
                regionName = parentRegion.name;
                regionAdcode = parentRegion.adcode;
                regionLevel = parentRegion.level;
                childrenNum = parentRegion.childrenNum;
              } else {
                regionName = regionAdcode = regionLevel = "";
              }
            }
          } else {
            regionName = regionAdcode = regionLevel = "";
          }

          // 更新当前选择的数据
          this.selectedName = regionName;
          this.selectedAdcode = regionAdcode;
          this.currentLevel = regionLevel;

          // 根据级别更新可用粒度选项
          this.updateAvailableGranularities(childrenNum === 0);

          // 粒度当前currentLevel的下一级
          this.granularity = this.allLevels[
            this.allLevels.indexOf(this.currentLevel) + 1
          ];

          // 确保粒度选项有效
          if (!this.availableGranularities.includes(this.granularity)) {
            this.granularity = "self";
          }

          // 发送文件上传事件
          this.$emit("file-upload", {
            fileName: file.name,
            content: jsonContent,
            name: regionName,
            adcode: regionAdcode,
            level: regionLevel,
          });

          // 同时更新URL
          this.updateUrls();
        } catch (error) {
          this.$Message.error("JSON格式错误，请检查文件内容");
          this.clearUploadedFile();
        }
      };

      reader.readAsText(file);

      // 阻止默认上传行为
      return false;
    },
    // 清除上传的文件
    clearUploadedFile() {
      this.uploadedFileName = "";
      this.uploadedFileContent = null;

      // 通知父组件
      this.$emit("file-upload", null);

      // 同时更新URL
      this.updateUrls();
    },
  },
};
</script>

<style scoped>
.shape-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f9;
  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  padding: 16px;
}

.shape-drawer-header {
  margin-bottom: 16px;
}

.shape-drawer-tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #515a6e;
  margin-bottom: 12px;
  background-color: #f0faff;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #2d8cf0;
}

.shape-drawer-tip i {
  margin-right: 8px;
}

.shape-drawer-search {
  margin-bottom: 16px;
}

.shape-drawer-current,
.shape-drawer-granularity,
.shape-drawer-api,
.shape-drawer-upload,
.shape-drawer-color {
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.card-title i {
  margin-right: 6px;
}

.current-content {
  padding: 4px 0;
}

.current-item {
  display: flex;
  margin-bottom: 8px;
}

.current-item:last-child {
  margin-bottom: 0;
}

.current-item .label {
  width: 60px;
  color: #808695;
}

.current-item .value {
  font-weight: 500;
  color: #17233d;
}

.granularity-options {
  padding: 4px 0;
}

.api-content {
  padding: 4px 0;
}

.api-url {
  margin-bottom: 8px;
  position: relative;
}

.upload-content {
  padding: 4px 0;
}

.file-info {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #f8f8f9;
  border-radius: 4px;
}

.color-content {
  padding: 4px 0;
  display: flex;
  flex-direction: column;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.color-label {
  margin-right: 8px;
  width: 60px;
  color: #808695;
}

.color-value {
  margin-left: 8px;
  color: #515a6e;
}

.shape-drawer >>> .ivu-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shape-drawer >>> .ivu-card-head {
  padding: 12px 16px;
  border-bottom: 1px solid #e8eaec;
}

.shape-drawer >>> .ivu-card-body {
  padding: 12px 16px;
}

.shape-drawer >>> .ivu-divider-horizontal {
  margin: 12px 0;
}

.shape-drawer >>> .ivu-radio-group-button .ivu-radio-wrapper {
  height: 32px;
  line-height: 30px;
  padding: 0 15px;
  font-size: 13px;
}

.shape-drawer >>> .ivu-select-selection {
  border-radius: 4px;
}
</style>
