<template>
    <div style="width: 100%;padding: 20px">
        <div id="plot"></div>
        <div>
            <Input v-model="data" type="textarea" :rows="3" placeholder="数据列表，多个值用逗号隔开" style="width: 800px"/>
            <Button type="primary" @click="action(data)" style="margin-left: 18px">提交</Button>
        </div>
        <div class="toolbar">
            <Button v-if="status" type="primary" @click="stop">结束接收</Button>
            <Button v-else type="primary" @click="start">开始接收</Button>
        </div>
    </div>
</template>

<script>
    // 引入 ECharts 主模块
    var echarts = require('echarts');

    export default {
        name: "chart-socket",
        mounted() {
            this.initPlot()
        },
        data() {
            return {
                chart: null,
                openModal: false,
                status: false,
                length: 10,
                data: '',
                option: {
                    title: {
                        text: '折线图'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>x：{b}<br/>y：{c}'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {}
                        }
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    legend: {
                        left: 'center',
                        data: ['新增值', '最小值', '下四分位', '中位数', '上四分位', '最大值', '门限值']
                    },
                    series: [
                        {
                            name: '新增值',
                            type: 'line',
                            data: [],
                        },
                        {
                            name: '最小值',
                            type: 'line',
                            data: [],
                        },
                        {
                            name: '下四分位',
                            type: 'line',
                            data: [],
                        },
                        {
                            name: '中位数',
                            type: 'line',
                            data: [],
                        },
                        {
                            name: '上四分位',
                            type: 'line',
                            data: [],
                        },
                        {
                            name: '最大值',
                            type: 'line',
                            data: [],
                        },
                        {
                            name: '门限值',
                            type: 'line',
                            data: [],
                        }
                    ]
                }
            }
        },
        computed: {},
        methods: {
            initPlot() {
                this.chart = echarts.init(document.getElementById("plot"))
                this.chart.setOption(this.option)
            },
            clearData() {
                this.option.xAxis.data = []
                this.option.series[0].data = []
                this.option.series[1].data = []
                this.option.series[2].data = []
                this.option.series[3].data = []
                this.option.series[4].data = []
                this.option.series[5].data = []
                this.option.series[6].data = []
                this.chart.setOption(this.option)
            },
            start() {
                this.$socket.emit('start', '')
            },
            stop() {
                this.$socket.emit('stop', '')
                this.status = false
            },
            action(data) {
                this.$socket.emit('action', data)
                this.status = false
            }
        },
        sockets: {
            connection(data) {
                window.console.log(data)
            },
            data(data) {
                let warFlow = JSON.parse(data)
                this.option.xAxis.data.push(warFlow.length)
                this.option.series[0].data.push(warFlow.newValue)
                this.option.series[1].data.push(warFlow.userCntMin)
                this.option.series[2].data.push(warFlow.lowerQuartile)
                this.option.series[3].data.push(warFlow.median)
                this.option.series[4].data.push(warFlow.upperQuartile)
                this.option.series[5].data.push(warFlow.userCntMax)
                this.option.series[6].data.push(warFlow.threshold)
                this.chart.setOption(this.option)
            },
            clear() {
                this.clearData()

                this.status = true
            },
        }
    }
</script>

<style scoped>
    #plot {
        height: 500px;
    }

    .toolbar {
        position: absolute;
        bottom: 10px;
        right: 10px;
    }
</style>