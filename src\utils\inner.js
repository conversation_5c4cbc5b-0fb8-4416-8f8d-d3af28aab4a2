const getInnerUrl = function(x, y, z) {
  return `/mapUrl/VectorMapAbility/HMapServer/v2.0/wmts?LAYER=ChinaMap2020Q1&SERVICE=WMTS&REQUEST=GetTile&TILEMATRIX=${z}&TILECOL=${x}&TILEROW=${y}&Version=1.0.0&FORMAT=image/png&style=&tilematrixset=EPSG:3857_base&key=ad6609c5afe3741b`;
};
export function initInnerUrl(wm) {
  if (wm.env && wm.env === 'inner') {
    const innerLayer = new AMap.TileLayer({
      getTileUrl: getInnerUrl,
    });
    return innerLayer;
  }
}
