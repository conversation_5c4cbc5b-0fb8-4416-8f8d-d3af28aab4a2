import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import less from "less";
import ViewUI from "view-design";
import "view-design/dist/styles/iview.css";
import "../my-theme/index.less";
import VCharts from "v-charts";
import VueClipboard from "vue-clipboard2";
import VueCodeMirror from "vue-codemirror";
import "codemirror/lib/codemirror.css";

window.ENV_VAR = process.env.VUE_APP_ENV_VAR;
Vue.use(less);
Vue.use(VueClipboard);
Vue.use(ViewUI);

Vue.config.productionTip = false;

Vue.use(VCharts);
Vue.use(VueCodeMirror);

new Vue({
  router,
  store,
  render: (h) => h(App),
  created() {
    // 初始化认证状态
    this.$store.dispatch("initializeAuth");
  },
}).$mount("#app");
