/**
 * 地图工具函数集合
 */

/**
 * 生成随机十六进制颜色
 * @returns {string} 十六进制颜色值，如 #FF0000
 */
export function randomHex() {
  return `#${Math.floor(Math.random() * 0xffffff)
    .toString(16)
    .padEnd(6, '0')}`;
}

/**
 * 更新地图中心点
 * @param {Array} dataArrays 坐标数组集合
 * @param {Object} map AMap实例
 * @returns {Array} 计算出的中心点坐标 [lng, lat]
 */
export function updateCenter(dataArrays, map) {
  let p = getCenter(dataArrays);
  if (p[0] > 0 && p[1] > 0 && map) {
    map.setCenter(new AMap.LngLat(p[0], p[1]), true);
  }
  return p;
}

/**
 * 计算一组坐标的中心点
 * @param {Array} dataArrays 坐标数组集合
 * @returns {Array} 计算出的中心点坐标 [lng, lat]
 */
export function getCenter(dataArrays) {
  let lng = 0;
  let lat = 0;
  let length = 0;
  dataArrays.forEach((data) => {
    data.forEach((d) => {
      lng += d[0];
      lat += d[1];
      length++;
    });
  });
  if (lng > 0 && lat > 0) {
    lng = lng / length;
    lat = lat / length;
    return [lng, lat];
  }
  return [0, 0];
}

/**
 * 获取标记选项配置
 * @param {Array} centerPoint 中心点坐标 [lng, lat]
 * @param {string} name 标记名称
 * @param {string} color 标记颜色
 * @returns {Object} 标记选项配置对象
 */
export function getLabelMarkerOptions(centerPoint, name, color, offset = [0, 0]) {
  return {
    position: new AMap.LngLat(centerPoint[0], centerPoint[1]),
    zIndex: 16,
    zooms: [3, 20],
    text: {
      content: name,
      direction: 'center',
      offset: offset,
      style: {
        fontSize: 14,
        fontWeight: 'normal',
        fillColor: color,
        strokeColor: '#ffffff',
        strokeWidth: 2,
      },
    },
  };
}

/**
 * 清除指定类型的图层
 * @param {string} type 图层类型 'point'|'polygon'|'track'|'heatmap'|'all'
 * @param {Object} mapState 地图状态对象，包含所有图层列表
 * @param {Object} map 地图实例
 * @param {Function} closePolyEdit 关闭多边形编辑的函数
 * @param {Function} showMessage 显示消息的函数
 * @param {boolean} showMsg 是否显示消息
 */
export function clearType(type, mapState, map, closePolyEdit, showMessage, showMsg = true) {
  const { massList, polygonList, pointLabelsLayers, polygonLabelsLayers, 
          heatmap, simplifierPathData, pathSimplifier, marker, keyDic } = mapState;
          
  if ((type === 'point' || type === 'all') && massList.length > 0) {
    massList.forEach((m) => m.setMap(null));
    massList.length = 0;
    pointLabelsLayers.forEach((layer) => map.remove(layer));
    marker.setPosition(new AMap.LngLat(0, 0));
  }
  
  if ((type === 'polygon' || type === 'all') && polygonList.length > 0) {
    polygonList.forEach((p) => map.remove(p.polygon));
    polygonList.length = 0;
    polygonLabelsLayers.forEach((layer) => map.remove(layer));
    closePolyEdit(true);
  }
  
  if ((type === 'track' || type === 'all') && pathSimplifier) {
    simplifierPathData.length = 0;
    pathSimplifier.setData(simplifierPathData);
    pathSimplifier.clearPathNavigators();
  }
  
  if ((type === 'heatmap' || type === 'all') && heatmap) {
    heatmap.setDataSet({
      data: [],
    });
  }
  
  if (showMsg && showMessage) {
    let msg = '';
    switch (type) {
      case 'all':
        msg = '所有图层';
        break;
      case 'point':
        msg = '海量点';
        break;
      case 'polygon':
        msg = '轮廓';
        break;
      case 'heatmap':
        msg = '热力图';
        break;
    }
    showMessage('清除' + msg + '完成');
  }
  
  if (keyDic) {
    if (type === 'all') {
      Object.keys(keyDic).forEach(key => delete keyDic[key]);
    } else {
      delete keyDic[type];
    }
  }
}

/**
 * 将十六进制颜色转换为RGBA格式
 * @param {string} hexColor 十六进制颜色，如 #FF0000
 * @param {number} alpha 透明度，0-1之间
 * @returns {string} RGBA格式的颜色字符串
 */
export function hexToRgba(hexColor, alpha = 0.05) {
  if (hexColor.startsWith('#')) {
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  return hexColor;
}

/**
 * 判断是否为直辖市
 * @param {string} adcode 行政区划代码
 * @returns {boolean} 是否为直辖市
 */
export function isMunicipality(adcode) {
  // 直辖市的adcode: 北京(110000), 天津(120000), 上海(310000), 重庆(500000), 香港(810000), 澳门(820000)
  const municipalityCodes = ['110000', '120000', '310000', '500000', '810000', '820000'];
  
  // 如果是完整的直辖市代码
  if (municipalityCodes.includes(adcode)) {
    return true;
  }
  
  // 如果是直辖市下属区县，判断前两位
  const prefix = adcode.substring(0, 2);
  return ['11', '12', '31', '50', '81', '82'].includes(prefix);
}

/**
 * 根据行政区划等级和代码获取可用的粒度选项
 * @param {string} level 行政区划等级 'country'|'province'|'city'|'district'
 * @param {string} adcode 行政区划代码
 * @param {boolean} noChild 没有子级
 * @returns {Array} 可用的粒度选项数组
 */
export function getAvailableGranularities(level, adcode, noChild = true) {  
  // 默认返回所有粒度
  if (!level) return [];
  
  // 特殊处理直辖市,香港
  if (isMunicipality(adcode)) {
    if (level === 'province') {
      return ['self', 'district']; // 直辖市级别只有市和区两级
    }
    if (level === 'district') {
      return ['self']; // 直辖市下属区县只有自身
    }
  }
  // 特殊处理台湾省
  if (adcode == '710000') {
    return ['self'];
  }

  const granularitiesMap = {
    'country': ['self', 'province', 'city'],
    'province': ['self', 'city', 'district'],
    'city': ['self', 'district'],
    'district': ['self']
  }
  if (noChild) {
    return granularitiesMap[level].slice(0, -1)
  }
  return granularitiesMap[level];
} 

export function createTextFile(text, filename) {
  const blob = new Blob([text], { type: 'text/plain' });
  return new File([blob], filename, { type: 'text/plain' });
}