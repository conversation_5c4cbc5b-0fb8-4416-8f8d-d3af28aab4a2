<template>
  <div class="amap-page-container">
    <div id="container" class="amap-demo" tabindex="0"></div>
    <div class="toolbar" v-if="!innerEnv">
      <ButtonGroup vertical>
        <Button icon="md-person" @click="openPersonalCenter"></Button>
        <Button
          icon="md-color-palette"
          @click="themePoptipVisible = !themePoptipVisible"
        ></Button>
        <Button @click="showVersion = !showVersion">{{ versionActive }}</Button>
        <Button icon="md-share" @click="shareModal = true"></Button>
        <Button icon="md-thumbs-up" @click="feedbackModal = true"></Button>
        <Button icon="md-time" @click="logModal = true"></Button>
        <Button icon="md-information-circle" @click="openDisModal"></Button>
      </ButtonGroup>
      <div class="theme-poptip" v-show="themePoptipVisible">
        <div
          v-for="item in mapStyles"
          :key="item.value"
          :class="{ active: item.value === themeActive }"
          @click="changeTheme(item.value)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div
      style="position: absolute;top: 20px;width: auto;left: 16px;right: 0;display: flex;"
    >
      <Cascader
        v-if="innerEnv"
        :data="cityData"
        v-model="city"
        clearable
        filterable
        :render-format="formatCityOption"
        placeholder="选择地市"
        style="width:100px"
      />
      <Select
        v-model="placeKeyWords"
        placeholder="查找地名"
        style="width: 300px"
        clearable
        filterable
        :remote-method="searchPlace"
        :loading="placeSearching"
        @on-change="selectPlace"
      >
        <Avatar
          v-if="innerEnv"
          :src="require('../assets/gaode.png')"
          slot="prefix"
          size="24"
        />
        <Option
          v-for="(item, index) in placeList"
          :key="index + item.location"
          :value="item.location.lng + '_' + item.location.lat"
          >{{ item.name }}
        </Option>
      </Select>
      <Dropdown @on-click="clearType" style="margin-left: 12px">
        <ButtonGroup>
          <Button type="primary">
            清除
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <Button
            type="primary"
            icon="md-trash"
            @click="clearType('all')"
          ></Button>
        </ButtonGroup>
        <DropdownMenu slot="list">
          <DropdownItem name="point">海量点</DropdownItem>
          <DropdownItem name="polygon">轮廓/折线</DropdownItem>
          <DropdownItem name="heatmap">热力图</DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Button type="primary" @click="openModal = true" style="margin-left: 12px"
        >开始打点
      </Button>
      <Button
        type="primary"
        @click="handleShapeDrawerOpen"
        :icon="openShapeDrawer ? 'md-checkmark' : ''"
        style="margin-left: 12px"
        >行政区轮廓
      </Button>
      <Button
        type="primary"
        @click="handleDrawPolygon"
        :icon="openDrawPolygon ? 'md-create' : ''"
        style="margin-left: 12px"
        >手绘多边形
      </Button>
    </div>
    <Modal
      v-model="openModal"
      title="上传经纬度数据"
      width="600"
      @on-ok="upload"
      :styles="{ top: '60px' }"
      draggable
    >
      <map-modal ref="modal"></map-modal>
    </Modal>
    <Drawer
      ref="shapeDrawerWrap"
      :width="shapeDrawerWidth"
      v-model="openShapeDrawer"
      :mask="false"
      @on-close="handleShapeDrawerClose"
      class-name="my-ivu-drawer-wrap"
    >
      <div slot="header">
        <span>行政区轮廓</span>
      </div>
      <shape-drawer
        ref="shapeDrawer"
        :visible="openShapeDrawer"
        @urls-update="handleShapeDrawerUpdate"
        @color-update="handleColorUpdate"
        @file-upload="handleFileUpload"
      ></shape-drawer>
      <div
        class="shape-drawer-hidden"
        :style="{ right: shapeDrawerWidth + '%' }"
        @click="handleShapeDrawerHidden"
      >
        <Icon
          :type="
            shapeDrawerWidth === 0 ? 'ios-arrow-back' : 'ios-arrow-forward'
          "
          size="24"
        />
      </div>
    </Drawer>
    <Drawer
      width="360"
      :value="polyEditName != null"
      :mask="false"
      @on-close="closePolyEdit(true)"
      class-name="my-ivu-drawer-wrap"
    >
      <div slot="header">{{ polyEditName }}</div>
      <poly-drawer
        ref="polyDrawer"
        :polyEditor="polyEditor"
        :name="polyEditName"
      ></poly-drawer>
    </Drawer>
    <Modal
      v-model="feedbackModal"
      title="意见反馈"
      width="600"
      ok-text="提交"
      @on-ok="submitFeedback"
      :styles="{ top: '60px' }"
      draggable
      :loading="feedbackLoading"
    >
      <feedback-modal ref="feedbackModal"></feedback-modal>
    </Modal>
    <Modal
      v-model="logModal"
      title="更新日志"
      width="600"
      :styles="{ top: '60px' }"
      footer-hide
      draggable
    >
      <log-modal ref="logModal"></log-modal>
    </Modal>
    <Modal
      v-model="shareModal"
      title="打点数据分享"
      width="600"
      :styles="{ top: '60px' }"
      footer-hide
      draggable
    >
      <share-modal ref="shareModal" :keyDic="keyDic"></share-modal>
    </Modal>

    <DisclaimerModal v-if="!disclaimerConfirmed" ref="dis" />
    <div v-if="showVersion" class="show-version">
      <RadioGroup
        v-model="versionActive"
        type="button"
        @click.native="selectVersion"
      >
        <Radio label="V1">V1版本</Radio>
        <Radio label="V2">V2版本</Radio>
      </RadioGroup>
    </div>
    <Modal
      v-model="personalCenter"
      class-name="personal-center-modal"
      :mask-closable="false"
      @on-cancel="handleModalClose"
    >
      <p>{{ personalCenterTitle }}</p>
      <div class="form-content">
        <div class="user-name">
          <span>用户名称:</span>
          <span>{{ $store.state.user ? $store.state.user.username : "" }}</span>
        </div>
        <div class="key-id">
          <span>Key标识:</span>
          <div class="key-input">
            <!-- 查看模式：已绑定key且不在编辑状态 -->
            <div v-if="!isFirstTimeUser && !isEditingKey" class="key-view-mode">
              <span class="key-display">{{ currentAmapKey }}</span>
              <Icon type="md-create" class="edit-icon" @click="enterEditMode" />
            </div>
            <!-- 编辑模式：首次绑定或点击编辑 -->
            <div v-else class="key-edit-mode">
              <Input
                v-model="amapKeyInput"
                size="small"
                class="key-input-input"
                placeholder="请输入高德地图key"
              />
              <transition name="validate-btn" mode="out-in">
                <Button
                  v-if="shouldShowValidateButton"
                  size="small"
                  type="primary"
                  class="key-check-btn"
                  :loading="keyValidating"
                  @click="validateAmapKey"
                  :title="isFirstTimeUser ? '校验key' : '重新校验key'"
                >
                  <Icon v-if="!isFirstTimeUser" type="md-refresh" />
                  {{ isFirstTimeUser ? "校验" : "" }}
                </Button>
              </transition>
            </div>
          </div>
        </div>
        <div class="key-tips">
          注: key获取方法请参考
          <a
            href="https://lbs.amap.com/api/javascript-api/guide/abc/prepare"
            target="_blank"
            rel="noopener noreferrer"
            >高德开发者文档“如何获取地图key”</a
          >
        </div>
        <div class="form-footer">
          <div class="form-footer-wrap">
            <!-- 只有在校验过后才显示结果 -->
            <span
              v-if="keyValidationStatus !== null"
              class="key-check-result"
              :class="{
                success: keyValidationStatus === 'valid',
                fail: keyValidationStatus === 'invalid',
              }"
            >
              {{
                keyValidationStatus === "valid" ? "有效Key标识" : "无效Key标识"
              }}
            </span>
            <!-- 确认按钮：编辑模式下显示 -->
            <div v-if="isFirstTimeUser || isEditingKey" class="edit-buttons">
              <Button
                type="primary"
                :disabled="!isConfirmButtonEnabled"
                :class="{ 'confirm-btn-disabled': !isConfirmButtonEnabled }"
                @click="confirmAmapKey"
              >
                确认
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import MapModal from "./MapModal";
import FeedbackModal from "./FeedbackModal";
import ShareModal from "./ShareModal.vue";
import PolyDrawer from "./PolyDrawer";
import ShapeDrawer from "./ShapeDrawer";
import LogModal from "./LogModal";
import { get, post } from "../api";
import { gcj02towgs84 } from "../common/CoordConvert";
import {
  randomSvg,
  revertColor,
  getPathDistance,
  yunEnv,
  innerEnv,
} from "../common/utils";
import { createTextFile } from "../utils/mapUtils";
import cityData from "../assets/json/city.json";
import { initInnerUrl } from "../utils/inner";
import { searchPoi } from "../utils/poi";
import DisclaimerModal from "./DisclaimerModal.vue";
import {
  randomHex,
  updateCenter,
  getCenter,
  getLabelMarkerOptions,
  isMunicipality,
  clearType as utilsClearType,
} from "../utils/mapUtils";
export default {
  name: "Map",
  computed: {
    innerEnv() {
      return innerEnv;
    },
    // 判断是否为首次登录用户（amapKey为空）
    isFirstTimeUser() {
      return (
        this.$store.state.user &&
        (!this.$store.state.user.amapKey ||
          this.$store.state.user.amapKey === "")
      );
    },
    // 弹窗标题
    personalCenterTitle() {
      return this.isFirstTimeUser ? "地图key绑定" : "用户信息";
    },
    // 当前显示的key值（用于查看模式）
    currentAmapKey() {
      return this.$store.state.user ? this.$store.state.user.amapKey || "" : "";
    },
    // 判断是否显示校验按钮（输入框有内容时才显示）
    shouldShowValidateButton() {
      return this.amapKeyInput && this.amapKeyInput.trim().length > 0;
    },
    // 判断确认按钮是否可用（只有校验通过时才可用）
    isConfirmButtonEnabled() {
      return this.keyValidationStatus === "valid";
    },
    // 获取当前高德地图key
    getEffectiveAmapKey() {
      const userKey = this.$store.state.user
        ? this.$store.state.user.amapKey
        : "";
      return userKey.trim();
    },
  },
  components: {
    MapModal,
    PolyDrawer,
    FeedbackModal,
    LogModal,
    ShareModal,
    DisclaimerModal,
    ShapeDrawer,
  },
  created() {
    const isConfirmed = localStorage.getItem("disclaimerConfirmed");
    if (isConfirmed) {
      this.disclaimerConfirmed = true;
    }
  },
  mounted() {
    this.onLoadMap();
    // 检查是否为首次登录用户，如果是则自动弹出personalCenter弹窗
    this.checkFirstTimeUser();
  },
  data() {
    return {
      city: [],
      cityData: cityData,
      gMap: null,
      openModal: false,
      openShapeDrawer: false,
      openDbModal: false,
      logModal: false,
      massList: [],
      polygonList: [],
      marker: null,
      infoWindow: null,
      heatmap: null,
      text: null,
      polyEditor: null,
      polyEditName: null,
      lnglat: null,
      feedbackModal: false,
      shareModal: false,
      feedbackLoading: true,
      rangingTool: null,
      openRangingTool: false,
      placeSearch: null,
      placeList: [],
      placeKeyWords: "",
      placeSearching: false,
      placeSearchMarker: null,
      overlays: [],
      pointLabelsLayers: [],
      polygonLabelsLayers: [],
      nameColorDelimiter: "$", //打点名称与颜色分隔符
      pathSimplifier: null,
      simplifierPathData: [],
      simplifierPathImg: null,
      searchTimer: null,
      changeTime: null, //隐藏延时
      keyDic: {},
      disclaimerConfirmed: false,
      version: ["1.4.15", "2.0"],
      versionActive: "V1",
      showVersion: false,
      shapeApiUrls: [],
      shapeDrawerWidth: 30, // 行政区轮廓抽屉宽度
      shapeStrokeColor: "#1890ff", // 默认边框颜色
      shapeFillColor: "rgba(24, 144, 255, 0.05)", // 默认填充颜色
      currentProcessingAdcode: "100000", // 当前正在处理的adcode
      currentProcessingGranularity: "self", // 当前正在处理的粒度
      isProcessingDrillOperation: false, // 是否正在处理下钻/上钻操作
      customInfoWindow: null, // 自定义信息窗口DOM元素
      customInfoWindowVisible: false, // 自定义信息窗口可见性
      customInfoWindowPosition: null, // 自定义信息窗口位置,
      themePoptipVisible: false,
      openDrawPolygon: false, // 正在手绘多边形
      mapStyles: [
        { value: "normal", name: "标准" },
        { value: "dark", name: "幻影黑" },
        { value: "light", name: "月光银" },
        { value: "whitesmoke", name: "远山黛" },
        { value: "fresh", name: "草色青" },
        { value: "grey", name: "雅士灰" },
        { value: "graffiti", name: "涂鸦" },
        { value: "macaron", name: "马卡龙" },
        { value: "blue", name: "靛青蓝" },
        { value: "darkblue", name: "极夜蓝" },
        { value: "wine", name: "酱籽" },
      ],
      themeActive: "normal",
      personalCenter: false,
      // personalCenter弹窗相关数据
      amapKeyInput: "", // 用户输入的key
      keyValidationStatus: null, // key校验状态：null-未校验, 'valid'-有效, 'invalid'-无效
      isEditingKey: false, // 是否处于编辑模式
      keyValidating: false, // 是否正在校验key
    };
  },
  watch: {
    versionActive: {
      handler(newV) {
        if (newV === "V2") {
          this.changeMapVersion(this.version[1]);
          localStorage.setItem("localVersion", this.version[1]);
        } else {
          this.changeMapVersion(this.version[0]);
          localStorage.setItem("localVersion", this.version[0]);
        }
      },
    },
    // 监听输入框内容变化
    amapKeyInput: {
      handler(newVal, oldVal) {
        // 当输入内容变化时，重置校验状态
        if (newVal !== oldVal) {
          this.keyValidationStatus = null;
        }
      },
      immediate: false,
    },
  },
  methods: {
    // 打开个人中心弹窗
    openPersonalCenter() {
      this.personalCenter = true;

      // 重置所有相关状态
      this.resetModalState();

      // 根据用户类型初始化输入框内容
      if (this.isFirstTimeUser) {
        // 首次用户：清空输入框，等待用户输入
        this.amapKeyInput = "";
      } else {
        // 已绑定用户：显示当前key值
        this.amapKeyInput = this.currentAmapKey;
      }
    },

    // 检查首次登录用户
    checkFirstTimeUser() {
      // 延迟检查，确保store已经初始化完成
      this.$nextTick(() => {
        if (this.isFirstTimeUser && this.$store.getters.isLoggedIn) {
          // 首次登录用户自动弹出personalCenter弹窗
          setTimeout(() => {
            this.openPersonalCenter();
          }, 500); // 延迟500ms确保页面加载完成
        }
      });
    },

    // 进入编辑模式
    enterEditMode() {
      // 重置校验相关状态
      this.keyValidationStatus = null;
      this.keyValidating = false;

      // 设置为编辑模式
      this.isEditingKey = true;

      // 初始化输入框为当前key值
      this.amapKeyInput = this.currentAmapKey;
    },

    // 处理弹窗关闭事件
    handleModalClose() {
      // 首次用户强制填写逻辑：检查是否已完成key绑定
      if (this.isFirstTimeUser && this.keyValidationStatus !== "valid") {
        this.$Message.warning("首次登录必须完成地图key绑定才能继续使用系统");

        // 确保用户已登录且路由存在
        if (this.$store.getters.isLoggedIn && this.$router) {
          this.$router.push("/").catch((err) => {
            window.console.warn("路由跳转失败:", err);
          });
        }

        // 阻止弹窗关闭，保持弹窗打开状态
        return false;
      }

      // 非首次用户或已完成绑定的用户：执行正常关闭逻辑
      this.handleNormalClose();
    },

    // 处理正常关闭逻辑
    handleNormalClose() {
      // 如果正在编辑，恢复到查看模式
      if (this.isEditingKey) {
        this.isEditingKey = false;
        this.keyValidationStatus = null;
        this.amapKeyInput = this.currentAmapKey;
      }

      // 关闭弹窗
      this.personalCenter = false;
    },

    // 校验高德地图key
    async validateAmapKey() {
      // 输入验证：检查输入框是否为空
      const trimmedKey = this.amapKeyInput ? this.amapKeyInput.trim() : "";
      if (!trimmedKey) {
        return;
      }

      // 开始校验流程
      this.keyValidating = true;

      try {
        // 调用校验 简单的key格式校验：32位字母数字组合
        const isValid = /^[a-zA-Z0-9]{32}$/.test(trimmedKey);

        // 更新校验状态
        this.keyValidationStatus = isValid ? "valid" : "invalid";
      } catch (error) {
        // 处理校验异常
        window.console.error("Key校验异常:", error);
        this.keyValidationStatus = "invalid";
      } finally {
        // 确保loading状态被重置
        this.keyValidating = false;
      }
    },

    // 确认保存key
    async confirmAmapKey() {
      // 检查按钮是否可用（双重保险）
      if (!this.isConfirmButtonEnabled) {
        this.$Message.warning("请先校验key");
        return;
      }

      try {
        // 更新用户信息中的amapKey
        const updatedUser = {
          ...this.$store.state.user,
          amapKey: this.amapKeyInput.trim(),
        };

        // 使用store action更新用户信息
        this.$store.dispatch("updateUser", updatedUser);

        this.$Message.success("Key绑定成功");

        // 重置所有相关状态
        this.resetModalState();

        // 关闭弹窗
        this.personalCenter = false;
        this.reinitializeMapWithNewKey();

        // 实际项目中应该调用API保存到后端
        // await this.updateUserAmapKey(this.amapKeyInput.trim());
      } catch (error) {
        this.$Message.error("保存失败，请稍后重试");
      }
    },

    // 重置弹窗相关状态
    resetModalState() {
      this.isEditingKey = false;
      this.keyValidationStatus = null;
      this.keyValidating = false;
      this.amapKeyInput = "";
    },

    onLoadMap() {
      //设置你的安全密钥
      window._AMapSecurityConfig = {
        securityJsCode: "72cbed61cfb4cb41faedab0090e93e2d",
      };
      // 移除window.onLoad回调，使用新的初始化时机
      // 初始化将在所有脚本加载完成后由loadMapScripts方法调用

      let localVersion = localStorage.getItem("localVersion");
      if (!localVersion) {
        localVersion = this.version[0];
      }
      const version = this.$route.params.version || localVersion;
      this.versionActive = version === "1.4.15" ? "V1" : "V2";
      this.changeMapVersion(version);
    },
    // 切换地图版本并重新加载地图API
    changeMapVersion(version) {
      const mapDom = document.getElementById("container");
      if (mapDom) {
        mapDom.innerHTML = "";
      }

      // 使用动态key替换硬编码key
      const effectiveKey = this.getEffectiveAmapKey;
      if (!effectiveKey) return;

      // 移除已存在的地图API脚本（如果有）
      this.removeExistingMapScript();

      // 动态加载AMap主库和Loca库
      this.loadMapScripts(version, effectiveKey);
    },

    // 动态加载地图相关脚本（按顺序加载）
    async loadMapScripts(version, key) {
      try {
        // 第一步：加载AMap主库
        await this.loadAmapScript(version, key);

        // 第二步：加载Loca库
        await this.loadLocaScript(key);

        // 第三步：检查并加载AMapUI（如果未加载）
        if (!window.AMapUI) {
          await this.loadAMapUIScript();
        }

        // 第四步：等待所有脚本就绪后初始化地图
        await this.waitForScriptsReady();

        // 第五步：初始化地图
        this.initializeMap();
      } catch (error) {
        window.console.warn("地图脚本加载过程中出现错误:", error);
      }
    },

    // 加载AMap主库脚本
    loadAmapScript(version, key) {
      return new Promise((resolve, reject) => {
        // 移除callback参数，因为我们使用新的初始化时机
        const url = `https://webapi.amap.com/maps?v=${version}&key=${key}`;
        const script = document.createElement("script");
        script.src = url;
        script.id = "amap-script";

        // 成功加载回调
        script.onload = () => {
          resolve();
        };

        // 错误处理
        script.onerror = () => {
          this.handleMapLoadError("AMap主库");
          reject(new Error("AMap主库加载失败"));
        };

        document.head.appendChild(script);
      });
    },

    // 加载Loca库脚本
    loadLocaScript(key) {
      return new Promise((resolve, reject) => {
        // Loca库版本配置
        const locaVersion = "1.3.2";
        const url = `https://webapi.amap.com/loca?v=${locaVersion}&key=${key}`;
        const script = document.createElement("script");
        script.src = url;
        script.id = "loca-script";

        // 成功加载回调
        script.onload = () => {
          resolve();
        };

        // 错误处理
        script.onerror = () => {
          this.handleMapLoadError("Loca库");
          reject(new Error("Loca库加载失败"));
        };

        document.head.appendChild(script);
      });
    },

    // 加载AMapUI库脚本
    loadAMapUIScript() {
      return new Promise((resolve, reject) => {
        // AMapUI库版本配置
        const url = "https://webapi.amap.com/ui/1.1/main.js?v=1.1.1";
        const script = document.createElement("script");
        script.src = url;
        script.id = "amapui-script";

        // 成功加载回调
        script.onload = () => {
          resolve();
        };

        // 错误处理
        script.onerror = () => {
          reject(new Error("AMapUI库加载失败"));
        };

        document.head.appendChild(script);
      });
    },

    // 等待所有脚本就绪
    waitForScriptsReady() {
      return new Promise((resolve) => {
        const checkReady = () => {
          if (window.AMap && window.Loca && window.AMapUI) {
            resolve();
          } else {
            // 如果脚本还未完全就绪，继续等待
            setTimeout(checkReady, 100);
          }
        };

        checkReady();
      });
    },

    // 初始化地图
    initializeMap() {
      try {
        this.init();
      } catch (error) {
        window.console.error("地图初始化失败:", error);
      }
    },

    // 移除已存在的地图API脚本
    removeExistingMapScript() {
      // 移除AMap主库脚本
      const existingAmapScript = document.getElementById("amap-script");
      if (existingAmapScript) {
        existingAmapScript.remove();
      }

      // 移除Loca库脚本
      const existingLocaScript = document.getElementById("loca-script");
      if (existingLocaScript) {
        existingLocaScript.remove();
      }

      // 移除AMapUI库脚本（如果是动态加载的）
      const existingAMapUIScript = document.getElementById("amapui-script");
      if (existingAMapUIScript) {
        existingAMapUIScript.remove();
      }

      // 清理全局变量
      if (window.AMap) {
        delete window.AMap;
      }
      if (window.Loca) {
        delete window.Loca;
      }
      // 只有在动态加载的情况下才清理AMapUI
      if (existingAMapUIScript && window.AMapUI) {
        delete window.AMapUI;
      }
    },

    // 处理地图API加载错误
    handleMapLoadError(libraryName = "地图API") {
      const userKey = this.$store.state.user
        ? this.$store.state.user.amapKey
        : "";

      if (userKey && userKey.trim() !== "") {
        // 用户有绑定key但加载失败，可能是key无效
        this.$Message.error(
          `${libraryName}加载失败，您绑定的key可能无效，请重新绑定有效的key`
        );
        // 自动打开个人中心让用户重新绑定
        setTimeout(() => {
          this.openPersonalCenter();
        }, 1000);
      } else {
        // 使用默认key也失败，可能是网络问题
        this.$Message.error(`${libraryName}加载失败，请检查网络连接`);
      }
    },

    // 使用新key重新初始化地图
    reinitializeMapWithNewKey() {
      try {
        // 保存当前地图状态
        const currentCenter = this.gMap ? this.gMap.getCenter() : null;
        const currentZoom = this.gMap ? this.gMap.getZoom() : 10;
        const currentMapStyle = this.themeActive;

        // 清理当前地图实例
        if (this.gMap) {
          this.gMap.destroy();
          this.gMap = null;
        }

        // 获取当前版本
        const currentVersion =
          this.versionActive === "V1" ? this.version[0] : this.version[1];

        // 重新加载地图API
        this.changeMapVersion(currentVersion);

        // 等待地图API加载完成后恢复状态
        const checkMapLoaded = () => {
          if (window.AMap && this.gMap) {
            // 恢复地图状态
            if (currentCenter) {
              this.gMap.setCenter(currentCenter);
            }
            this.gMap.setZoom(currentZoom);
            if (currentMapStyle !== "normal") {
              this.changeTheme(currentMapStyle);
            }
          } else {
            // 如果地图还未加载完成，继续等待
            setTimeout(checkMapLoaded, 500);
          }
        };

        // 延迟检查，给API加载一些时间
        setTimeout(checkMapLoaded, 500);
      } catch (error) {
        window.console.error("地图重新初始化失败:", error);
      }
    },

    selectVersion() {
      setTimeout(() => {
        this.showVersion = false;
      }, 500);
    },
    init: function() {
      this.$Message.config({
        top: 50,
        duration: 10,
      });
      const mapOptions = {
        resizeEnable: true,
        doubleClickZoom: false,
        keyboardEnable: false,
        zoom: 10, //设置地图显示的缩放级别
        zooms: [3, 20],
        expandZoomRange: true,
      };
      if (innerEnv) {
        mapOptions.layers = [initInnerUrl(this)];
      }
      this.gMap = new AMap.Map("container", mapOptions);

      //   加载内网地图；
      //   initInnerUrl(this);
      this.gMap.plugin(
        [
          "AMap.Scale",
          "AMap.MapType",
          "AMap.PolyEditor",
          "AMap.Polyline",
          "AMap.RangingTool",
          "AMap.PlaceSearch",
          this.versionActive === "V1" ? "AMap.Heatmap" : "AMap.HeatMap",
        ],
        () => {
          this.gMap.addControl(new AMap.Scale());
          if (!innerEnv) {
            this.gMap.addControl(new AMap.MapType());
            this.placeSearch = new AMap.PlaceSearch({
              type: "",
              extensions: "base",
            });
          }
          //初始化heatmap对象
          this.heatmap = new AMap[
            this.versionActive === "V1" ? "Heatmap" : "HeatMap"
          ](this.gMap, {
            radius: 30,
          });

          this.rangingTool = new AMap.RangingTool(this.gMap);
          this.rangingTool.on("end", () => {
            this.rangingTool.turnOff();
            this.openRangingTool = false;
          });
        }
      );
      this.marker = new AMap.Marker({
        content: " ",
        offset: new AMap.Pixel(-5, -15),
        map: this.gMap,
      });
      this.infoWindow = new AMap.InfoWindow({ content: " ", map: this.gMap });
      this.placeSearchMarker = new AMap.Marker({
        position: new AMap.LngLat(0, 0),
        offset: new AMap.Pixel(-5, -15),
        map: this.gMap,
      });
      this.loadMapEvent();
      AMapUI.load(["ui/misc/PathSimplifier"], (PathSimplifier) => {
        if (!PathSimplifier.supportCanvas) {
          window.return;
        }
        this.initPathSimplifier(this.gMap, PathSimplifier);
      });

      // 创建自定义信息窗口
      this.createCustomInfoWindow();

      if (innerEnv) {
        this.changeCss();
      }
    },
    changeCss() {
      this.changeTime = setTimeout(() => {
        if (!document.querySelector(".amap-logo")) {
          this.changeTime && clearTimeout(this.changeTime);
          this.changeCss();
          return;
        }
        document.querySelector(".amap-logo").style.visibility = "hidden";
        document.querySelector(".amap-copyright").style.visibility = "hidden";
      }, 300);
    },
    formatCityOption(labels, selectedData) {
      const index = labels.length - 1;
      const data = selectedData[index] || false;
      if (data && data.code) {
        return labels[index] + " - " + data.code;
      }
      return labels[index];
    },
    upload() {
      this.$Spin.show();
      this.reloadUrl();
      let formData = this.$refs.modal.formItem;
      // 栅格类型：统一走文件上传接口
      if (formData.type === "grid") {
        const modal = this.$refs.modal;

        // 解析栅格数据并构造新格式
        this.gridMeta = [];
        if (modal.fileType === "text") {
          try {
            // 解析用户输入的栅格文本数据
            const parsedGridData = this.parseGridTextData(modal.fileText);
            // 将解析后的数据转换为文件
            modal.file = createTextFile(parsedGridData, "grid.txt");
          } catch (error) {
            this.$Message.error("栅格数据格式错误：" + error.message);
            this.closeSpin();
            return;
          }
        }

        this.uploadByGridFile();
        return;
      }
      if (this.$refs.modal.fileType === "text") {
        formData.file = new Blob([this.$refs.modal.fileText], {
          type: "text/html;charset=utf-8",
        });
        this.uploadByFile(formData);
      } else if (this.$refs.modal.fileType === "file") {
        formData.file = this.$refs.modal.file;
        this.uploadByFile(formData);
      } else if (this.$refs.modal.fileType === "mysql") {
        let tableForm = this.$refs.modal.$refs.mysqlInfo.tableForm;
        formData.sqlText = this.$refs.modal.sqlText;
        for (const item in tableForm) {
          formData[item] = tableForm[item];
        }
        this.uploadByMysql(formData);
      }
    },

    /**
     * 解析栅格文本数据并转换为新格式
     */
    parseGridTextData(textData) {
      if (!textData || !textData.trim()) {
        throw new Error("栅格数据不能为空");
      }

      const lines = textData
        .trim()
        .split("\n")
        .filter((line) => line.trim());
      const parsedLines = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split("|");
        if (parts.length < 3) {
          throw new Error(`第${i + 1}行格式错误：字段数量不足`);
        }

        const type = parts[0].trim();
        let formattedLine = "";

        if (type === "0") {
          // 按栅格ID查询：查询类型|栅格id列表|名称$颜色
          if (parts.length !== 3) {
            throw new Error(`第${i + 1}行格式错误：type=0时应为3个字段`);
          }
          const gridIds = parts[1].trim();
          const nameAndColor = parts[2].trim();

          // 验证栅格ID格式
          if (!gridIds || !gridIds.match(/^[\d,\s]+$/)) {
            throw new Error(`第${i + 1}行栅格ID格式错误：应为数字和逗号组成`);
          }

          formattedLine = `${type}|${gridIds}|${nameAndColor}`;
        } else if (type === "1") {
          // 按中心坐标查询：查询类型|栅格等级|中心经纬度列表|名称$颜色
          if (parts.length !== 4) {
            throw new Error(`第${i + 1}行格式错误：type=1时应为4个字段`);
          }
          const level = parts[1].trim();
          const coordinates = parts[2].trim();
          const nameAndColor = parts[3].trim();

          // 验证栅格等级
          if (!level || !level.match(/^\d+$/)) {
            throw new Error(`第${i + 1}行栅格等级格式错误：应为数字`);
          }

          // 验证经纬度格式
          if (!this.validateCoordinates(coordinates)) {
            throw new Error(`第${i + 1}行经纬度格式错误`);
          }

          formattedLine = `${type}|${level}|${coordinates}|${nameAndColor}`;
        } else if (type === "2") {
          // 按边长查询：查询类型|边长|中心经纬度列表|名称$颜色
          if (parts.length !== 4) {
            throw new Error(`第${i + 1}行格式错误：type=2时应为4个字段`);
          }
          const sideLength = parts[1].trim();
          const coordinates = parts[2].trim();
          const nameAndColor = parts[3].trim();

          // 验证边长
          if (!sideLength || !sideLength.match(/^\d+(\.\d+)?$/)) {
            throw new Error(`第${i + 1}行边长格式错误：应为数字`);
          }

          // 验证经纬度格式
          if (!this.validateCoordinates(coordinates)) {
            throw new Error(`第${i + 1}行经纬度格式错误`);
          }

          formattedLine = `${type}|${sideLength}|${coordinates}|${nameAndColor}`;
        } else {
          throw new Error(`第${i + 1}行查询类型错误：支持的类型为0、1、2`);
        }

        parsedLines.push(formattedLine);
      }

      return parsedLines.join("\n");
    },

    /**
     * 验证经纬度格式
     */
    validateCoordinates(coordinates) {
      if (!coordinates) return false;

      // 经纬度格式：lng1,lat1;lng2,lat2
      const coordPairs = coordinates.split(";");
      for (const pair of coordPairs) {
        const coords = pair.trim().split(",");
        if (coords.length !== 2) return false;

        const lng = parseFloat(coords[0].trim());
        const lat = parseFloat(coords[1].trim());

        if (isNaN(lng) || isNaN(lat)) return false;
        if (lng < -180 || lng > 180 || lat < -90 || lat > 90) return false;
      }

      return true;
    },

    /**
     * 栅格上传 - 文件模式
     */
    uploadByGridFile() {
      const modal = this.$refs.modal;
      const dataObj = { file: modal.file };
      post("/s2/parseS2File", dataObj).then((response) => {
        this.parseGridResponse(response);
      });
    },

    /**
     * 解析栅格接口返回
     */
    parseGridResponse(response) {
      const resp = response.data || {};
      if ((resp.status && resp.status === "true") || resp.code === 1) {
        const pColor = this.getHexColor(this.$refs.modal.myColor);
        this.parseGridData(resp, pColor);
      } else {
        this.$Message.warning(resp.hint || "查询失败");
      }
      this.closeSpin();
    },

    /**
     * 处理栅格数据为多边形绘制格式
     */
    parseGridData(data, pColor) {
      if (!data || !data.dataList) return;
      const list = data.dataList.map((item, index) => {
        const pointArr = (item.gridShape || "")
          .split(";")
          .filter((s) => s.trim() !== "")
          .map((coord) => {
            const [lng, lat] = coord.split(",").map((v) => Number(v.trim()));
            return { lng, lat, wgsLng: lng, wgsLat: lat };
          });

        // 名称与颜色处理
        let name = this.$refs.modal.autoIncrementName
          ? (index + 1).toString()
          : item.name || ""; // 优先使用自动编号或接口返回的 name
        let colorSuffix = item.colour
          ? this.nameColorDelimiter + item.colour
          : ""; // 优先使用接口返回的 colour

        if (!name && this.gridMeta && this.gridMeta[index]) {
          const meta = this.gridMeta[index];
          name = meta.name || "";
          if (meta.color && !colorSuffix) {
            colorSuffix = this.nameColorDelimiter + meta.color;
          }
        }

        const finalName = name + colorSuffix;
        return {
          name: finalName,
          pointList: [pointArr],
        };
      });

      const converted = {
        list: list,
      };

      this.addPolygonList(
        converted,
        pColor,
        false,
        this.$refs.modal.randomColor
      );
    },
    /**
     * @name: 处理自定义颜色数据
     * @description: 唐总需求支持单个标注点自定义颜色#20210713（仅支持字符串输入）
     * 若名称中含有指定分隔符，则切割其后的作为颜色值并还原名称
     */
    handleDiffColorData(datas, defaultColor) {
      const result = new Map();
      for (let item of datas) {
        let color = defaultColor;
        if (item.name.includes(this.nameColorDelimiter)) {
          const tempArr = item.name.split(this.nameColorDelimiter);
          item.name = tempArr[0];
          color = tempArr[1];
        }
        if (result.has(color) === false) {
          result.set(color, []);
        }
        result.get(color).push(item);
      }
      return result;
    },
    uploadByFile(formData) {
      this.uploadData(
        "/coordinate/convert/",
        "/coordinate/convert/area/",
        formData
      );
    },
    uploadByMysql(formData) {
      this.uploadData(
        "/coordinate/mysql/convert/",
        "/coordinate/mysql/convert/area/",
        formData
      );
    },
    uploadData(pointUrl, polygonUrl, formData) {
      if (formData.bigType === "point") {
        post(pointUrl, formData).then((response) => {
          this.parseResponse(response);
        });
      } else if (formData.bigType === "polygon") {
        if (formData.type === "polyline") {
          let formData = this.$refs.modal.formItem;
          formData.closeLoop = false;
        }
        post(polygonUrl, formData).then((response) => {
          this.parseResponse(response);
        });
      }
    },
    parseResponse(response) {
      let data = response.data.data;
      if (response.data.code === 1) {
        this.parseResponseData(data);
      } else {
        this.$Message.warning(response.data.msg);
      }
      this.closeSpin();
    },
    //转化颜色
    getHexColor(color) {
      var values = color
        .replace(/rgba?\(/, "")
        .replace(/\)/, "")
        .replace(/[\s+]/g, "")
        .split(",");
      var a = parseFloat(values[3] || 1),
        r = Math.floor(a * parseInt(values[0]) + (1 - a) * 255),
        g = Math.floor(a * parseInt(values[1]) + (1 - a) * 255),
        b = Math.floor(a * parseInt(values[2]) + (1 - a) * 255);
      return (
        "#" +
        ("0" + r.toString(16)).slice(-2) +
        ("0" + g.toString(16)).slice(-2) +
        ("0" + b.toString(16)).slice(-2)
      );
    },
    parseResponseData(data) {
      let type = data.type;
      let bigType = data.bigType;
      let myColor = this.$refs.modal.myColor;
      let pColor = this.getHexColor(myColor);
      if (bigType === "point") {
        this.parsePointData(data, type, pColor);
      } else if (bigType === "polygon") {
        this.parsePolygonData(data, type, pColor);
      }
      if (data.key && data.key.length > 0) {
        if (this.keyDic[type] && this.keyDic[type].length > 0) {
          this.$set(this.keyDic, type, this.keyDic[type].concat(data.key));
        } else {
          this.$set(this.keyDic, type, [data.key]);
        }
      }
    },
    parsePointData(data, type, pColor) {
      let datas = data.list.map((m, index) => {
        return {
          lnglat: [m["lng"], m["lat"]],
          name: this.$refs.modal.autoIncrementName ? index + 1 + "" : m["name"],
        };
      });
      if (type === "point") {
        // this.addMarks(datas, pColor);
        datas = this.handleDiffColorData(datas, pColor);
        for (let [key, value] of datas) {
          this.addMarks(value, key);
        }
      } else if (type === "heatmap") {
        let dataHeat = datas.map((o) => {
          return {
            lng: o["lnglat"][0],
            lat: o["lnglat"][1],
            count: o["name"],
          };
        });
        this.addHeatMap(dataHeat);
      } else if (type === "track") {
        let dataTrack = datas.map((o) => {
          return o["lnglat"];
        });
        this.addTrack(dataTrack, pColor);
      }
    },
    parsePolygonData(data, type, pColor) {
      this.addPolygonList(
        data,
        pColor,
        type === "polyline",
        this.$refs.modal.randomColor
      );
    },
    addMarks(datas, pColor) {
      this.updateCenter(datas.map((data) => [data.lnglat]));
      let mass = new AMap.MassMarks(datas, {
        opacity: 0.8,
        zIndex: 111,
        cursor: "pointer",
        alwaysRender: false,
        style: {
          url: randomSvg(pColor),
          anchor: new AMap.Pixel(5, 5),
          size: new AMap.Size(10, 10),
        },
      });
      if (this.$refs.modal.pointShowName) {
        let pointLabelsLayer = new AMap.LabelsLayer(
          this.getLabelsLayerOptions()
        );
        datas.forEach((data) => {
          const labelMarker = new AMap.LabelMarker(
            this.getLabelMarkerOptions(data.lnglat, data.name, pColor, [0, 15])
          );
          pointLabelsLayer.add(labelMarker);
        });
        this.gMap.add(pointLabelsLayer);
        this.pointLabelsLayers.push(pointLabelsLayer);
      } else {
        mass.on("mouseover", (e) => {
          this.marker.setPosition(e.data.lnglat);
          this.marker.setLabel({ content: e.data.name });
        });
        mass.on("mouseout", () => {
          this.marker.setPosition(new AMap.LngLat(0, 0));
        });
      }
      mass.setMap(this.gMap);
      this.massList.push(mass);
    },
    addPolygonList(datas, pColor = "blue", line = false, randomColor = false) {
      let polygonLabelsLayer = new AMap.LabelsLayer(
        this.getLabelsLayerOptions()
      );
      datas.list.forEach((a) => {
        let dataArray = a.pointList.map((polygon) =>
          polygon.map((m) => [m["lng"], m["lat"], m["wgsLng"], m["wgsLat"]])
        );

        // 基础颜色（拾色器 color 或上层传入颜色）
        let polygonColor = pColor;

        // 名称里自带颜色
        if (a.name && a.name.includes(this.nameColorDelimiter)) {
          const tempArr = a.name.split(this.nameColorDelimiter);
          a.name = tempArr[0];
          polygonColor = tempArr[1];
        }

        // 随机颜色优先生效（仅当勾选随机）
        if (randomColor) {
          polygonColor = randomHex();
        }

        this.addPolygonItem(
          polygonLabelsLayer,
          dataArray,
          a.name,
          polygonColor,
          line
        );
      });
      this.gMap.add(polygonLabelsLayer);
      this.polygonLabelsLayers.push(polygonLabelsLayer);
    },
    addPolygonItem(
      layer,
      dataArray,
      name = "轮廓",
      pColor = "blue",
      line = false
    ) {
      const showName = this.$refs.modal && this.$refs.modal.pointShowName;

      return addPolygon(
        this.gMap,
        layer,
        dataArray,
        name,
        pColor,
        line,
        this.polygonList,
        this.editPoly.bind(this),
        this.contextMenu,
        showName
      );
      function addPolygon(
        map,
        layer,
        dataArray,
        name = "轮廓",
        pColor = "blue",
        line = false,
        polygonList = [],
        editPolyHandler,
        contextMenu,
        showName = true
      ) {
        let centerPoint = updateCenter(dataArray, map);
        let polygon;
        let pointPath;

        if (line) {
          polygon = new AMap.Polyline();
          pointPath = dataArray.flatMap((data) =>
            data.map((d) => new AMap.LngLat(d[0], d[1]))
          );
        } else {
          polygon = new AMap.Polygon();
          pointPath = dataArray.map((data) =>
            data.map((d) => new AMap.LngLat(d[0], d[1]))
          );
        }

        polygon.setOptions({
          path: pointPath,
          borderWeight: 2, // 线条宽度，默认为 1
          fillOpacity: 0,
          strokeColor: pColor, // 线条颜色
        });

        map.add(polygon);
        polygon.setExtData(
          dataArray.map((data) => data.map((d) => [d[2], d[3]]))
        );

        let index = polygonList.length;

        if (editPolyHandler) {
          polygon.on("dblclick", (e) => {
            editPolyHandler(e, index);
          });
        }

        if (contextMenu) {
          polygon.on("rightclick", (e) => {
            contextMenu.open(map, e.lnglat);
          });
        }

        if (showName && layer) {
          let labelMarker = new AMap.LabelMarker(
            getLabelMarkerOptions(centerPoint, name, pColor)
          );
          layer.add(labelMarker);
        }

        polygonList.push({
          polygon: polygon,
          name: name,
          id: polygon._amap_id,
          show: true,
        });

        return polygon;
      }
    },
    editPoly(e, index) {
      this.closePolyEdit(this.polyEditName != null);
      this.polyEditor = new AMap.PolyEditor(this.gMap, e.target);
      this.polyEditor.open();
      this.polyEditor.on("adjust", (e) => this.$refs.polyDrawer.updatePoint(e));
      this.polyEditor.on("removenode", (e) =>
        this.$refs.polyDrawer.updatePoint(e)
      );
      this.polyEditName = this.polygonList[index].name;
    },
    closePolyEdit(forceClear) {
      if (this.polyEditor != null && forceClear) {
        this.polyEditor.close();
        this.polyEditName = null;
        return true;
      }
      return false;
    },
    addHeatMap(datas) {
      updateCenter(
        datas.map((d) => [[d["lng"], d["lat"]]]),
        this.map
      );
      this.heatmap.setDataSet({
        data: datas,
      });
    },
    addTrack(datas, pColor) {
      this.updateCenter(datas.map((d) => [d]));
      this.simplifierPathData.push({
        color: pColor,
        name: "轨迹" + (this.simplifierPathData.length + 1),
        path: datas,
      });
      this.pathSimplifier.setData(this.simplifierPathData);
      //创建一个巡航器
      this.simplifierPathData.forEach((s, index) =>
        this.pathSimplifier
          .createPathNavigator(index, {
            loop: true, //循环播放
            speed: (getPathDistance(s.path) * 3.6) / 30,
          })
          .start()
      );
    },
    setCenter(position) {
      if (position) {
        let lng = position.split("_")[0];
        let lat = position.split("_")[1];
        this.gMap.setCenter(new AMap.LngLat(lng, lat), true);
        return new AMap.LngLat(lng, lat);
      }
      return new AMap.LngLat(0, 0);
    },
    updateCenter(dataArrays) {
      return updateCenter(dataArrays, this.gMap);
    },
    getCenter(dataArrays) {
      return getCenter(dataArrays);
    },
    clearType(type, showMsg = true) {
      const mapState = {
        massList: this.massList,
        polygonList: this.polygonList,
        pointLabelsLayers: this.pointLabelsLayers,
        polygonLabelsLayers: this.polygonLabelsLayers,
        heatmap: this.heatmap,
        simplifierPathData: this.simplifierPathData,
        pathSimplifier: this.pathSimplifier,
        marker: this.marker,
        keyDic: this.keyDic,
      };

      const showMessage = (message) => {
        this.$Message.info({
          content: message,
          duration: 1,
        });
      };

      utilsClearType(
        type,
        mapState,
        this.gMap,
        this.closePolyEdit.bind(this),
        showMessage,
        showMsg
      );
    },
    closeSpin() {
      this.$nextTick(() => {
        this.$Spin.hide();
      });
    },
    loadMapEvent() {
      this.loadContextMenu();
      this.gMap.on("rightclick", (e) => {
        this.lnglat = e.lnglat;
        this.contextMenu.open(this.gMap, e.lnglat);
      });
      this.gMap.on("dblclick", (e) => {
        this.lnglat = e.lnglat;
        this.showLngLat(e.lnglat);
      });

      // 添加地图点击事件，实现上钻功能
      this.gMap.on("click", (e) => {
        // 如果抽屉未打开，不处理点击事件
        if (!this.openShapeDrawer) return;

        // 判断点击位置是否在已有轮廓内
        let isOutsidePolygon = true;

        // 遍历所有多边形，检查点击位置是否在其中
        for (const item of this.polygonList) {
          if (
            item.polygon &&
            typeof item.polygon.contains === "function" &&
            item.polygon.contains(e.lnglat)
          ) {
            isOutsidePolygon = false;
            break;
          }
        }

        isOutsidePolygon && this.drillUp();
      });
    },
    loadContextMenu() {
      this.contextMenu = new AMap.ContextMenu();
      this.contextMenu.addItem(
        "开始打点",
        () => {
          this.openModal = true;
          this.contextMenu.close();
        },
        0
      );
      this.contextMenu.addItem(
        "标记位置",
        () => {
          this.showLngLat(this.lnglat);
          this.contextMenu.close();
        },
        10
      );
      this.contextMenu.addItem(
        "地图测距",
        () => {
          this.rangingTool.turnOn();
          this.openRangingTool = true;
          this.contextMenu.close();
        },
        15
      );
      this.contextMenu.addItem(
        "清除海量点",
        () => {
          this.clearType("point");
          this.contextMenu.close();
        },
        20
      );
      this.contextMenu.addItem(
        "清除轮廓/折线",
        () => {
          this.clearType("polygon");
          this.contextMenu.close();
        },
        30
      );
      this.contextMenu.addItem(
        "清除热力图",
        () => {
          this.clearType("heatmap");
          this.contextMenu.close();
        },
        40
      );
      this.contextMenu.addItem(
        "清除所有图层",
        () => {
          this.clearType("all");
          this.contextMenu.close();
        },
        50
      );
    },
    showLngLat(point) {
      if (
        !this.closePolyEdit(this.polyEditName != null) &&
        this.lnglat &&
        !this.openRangingTool
      ) {
        this.infoWindow.setPosition(point);
        let lnglat = gcj02towgs84(point.lng, point.lat);
        this.infoWindow.setContent(
          lnglat[0].toFixed(7) + "," + lnglat[1].toFixed(7)
        );
        this.infoWindow.open(this.gMap);
      }
    },
    submitFeedback() {
      if (!yunEnv) {
        let formData = this.$refs.feedbackModal.formItem;
        let form = this.$refs.feedbackModal.$refs.feedbackForm;
        form.validate((valid) => {
          if (valid) {
            this.$Spin.show();
            post("/coordinate/feedback", formData).then((response) => {
              if (response.data.code === 1) {
                form.resetFields();
                this.feedbackModal = false;
                this.$Message.info("提交成功，谢谢您的反馈！");
              } else {
                this.$Message.warning(response.data.msg);
              }
              this.closeSpin();
            });
          } else {
            this.feedbackLoading = false;
            this.$nextTick(() => {
              this.feedbackLoading = true;
            });
          }
        });
      } else {
        this.feedbackModal = false;
        this.$Message.info("提交成功，谢谢您的反馈！");
      }
    },
    getLabelMarkerOptions(point, name, color, offset = [0, 0]) {
      return getLabelMarkerOptions(point, name, color, offset);
    },
    getLabelsLayerOptions() {
      return {
        zooms: [6, 20],
        zIndex: 100,
      };
    },

    searchPlace(keyWords) {
      if (innerEnv) {
        if (!this.city || this.city.length === 0) {
          this.$Message.warning("请先选择地市");
          return;
        }

        if (this.searchTimer) {
          clearTimeout(this.searchTimer);
          this.searchTimer = null;
        }
        this.searchTimer = setTimeout(() => {
          this.placeList = [];
          searchPoi(keyWords, this.city[this.city.length - 1])
            .then((res) => {
              const data = res.map((item) => {
                return {
                  name: item.NAME,
                  location: {
                    Q: item.Y * 1,
                    R: item.X * 1,
                    lng: item.X * 1,
                    lat: item.Y * 1,
                  },
                };
              });
              this.placeList = data;
            })
            .catch();
        }, 300);
        return;
      }
      this.placeSearch.search(keyWords, (status, result) => {
        this.placeList = [];
        if (status === "complete") {
          this.placeList = result.poiList.pois.map((poi) => {
            return {
              name: poi.name,
              location: poi.location,
            };
          });
        }
      });
    },
    selectPlace(point) {
      if (!point) {
        return;
      }
      let centerPoint = this.setCenter(point);
      this.placeSearchMarker.setPosition(centerPoint);
    },
    initPathSimplifier(map, PathSimplifier) {
      this.simplifierPathImg = PathSimplifier.Render.Canvas.getImageContent(
        require("../assets/ikun.png"),
        () => {},
        () => {}
      );
      this.pathSimplifier = new PathSimplifier({
        zIndex: 100,
        map: map, //所属的地图实例
        getPath: (pathData) => {
          return pathData.path;
        },
        getHoverTitle: (pathData, pathIndex, pointIndex) => {
          if (pointIndex >= 0) {
            //鼠标悬停在某个轨迹节点上
            return (
              pathData.name + "，点:" + pointIndex + "/" + pathData.path.length
            );
          }
          //鼠标悬停在节点之间的连线上
          return pathData.name + "，点数量" + pathData.path.length;
        },
        renderOptions: {
          pathLineStyle: {
            dirArrowStyle: true,
          },
          getPathStyle: (pathItem) => {
            return {
              //轨迹点的样式
              startPointStyle: {
                radius: 6,
                fillStyle: "green",
                strokeStyle: "green",
              },
              endPointStyle: {
                radius: 6,
                fillStyle: "red",
                strokeStyle: "red",
              },
              //轨迹线的样式
              pathLineStyle: {
                strokeStyle: pathItem.pathData.color,
                lineWidth: 6,
                dirArrowStyle: true,
              },
              //经过路径的样式
              pathNavigatorStyle: {
                width: 24,
                height: 24,
                // content: this.simplifierPathImg,
                pathLinePassedStyle: {
                  lineWidth: 6,
                  strokeStyle: revertColor(pathItem.pathData.color),
                },
              },
            };
          },
        },
      });
    },
    beforeDestroy() {
      if (this.changeTime) clearTimeout(this.changeTime);
    },
    loadSharedUrl() {
      let key = this.$route.params.key;
      if (key && key.length === 28) {
        this.$Spin.show();
        get("/shared/query?key=" + key).then((response) => {
          let data = response.data.data;
          if (response.data.code === 1) {
            data.list.forEach((d) => {
              this.parseResponseData(d);
            });
          } else {
            this.$Message.warning(response.data.msg);
          }
          this.closeSpin();
        });
      }
    },
    reloadUrl() {
      // 重置路由
      if (this.$route.params.key && this.$route.params.key !== "") {
        this.$router.push("/map");
      }
      // 重置分享
      this.$refs.shareModal.formItem.url = "";
    },
    openDisModal() {
      this.disclaimerConfirmed = false;
      this.$nextTick(() => {
        this.$refs.dis.openModal();
      });
    },

    // 处理ShapeDrawer打开事件
    handleShapeDrawerOpen() {
      if (this.openShapeDrawer) {
        this.openShapeDrawer = false;
        this.handleShapeDrawerClose();
        return;
      }
      this.openShapeDrawer = true;
      document.getElementById("container").style.width = "70%";
    },
    // 处理ShapeDrawer关闭事件
    handleShapeDrawerClose() {
      document.getElementById("container").style.width = "100%";
      this.clearType("polygon", false);
      // 重置处理状态
      this.currentProcessingGranularity = null;
      this.currentProcessingAdcode = null;
      this.isProcessingDrillOperation = false;
      // 关闭信息窗口
      this.hideCustomInfoWindow();
    },
    handleShapeDrawerHidden() {
      if (this.shapeDrawerWidth === 0) {
        this.shapeDrawerWidth = 30;
        document.getElementById("container").style.width = "70%";
        this.$refs.shapeDrawerWrap.$el.style.visibility = "visible";
      } else {
        this.shapeDrawerWidth = 0;
        document.getElementById("container").style.width = "100%";
        this.$refs.shapeDrawerWrap.$el.style.visibility = "hidden";
      }
    },

    // 统一处理ShapeDrawer的所有事件
    handleShapeDrawerUpdate(data) {
      // 保存URLs
      this.shapeApiUrls = {
        jsonUrl: data.jsonUrl,
        geojsonUrl: data.geojsonUrl,
      };

      // 处理区域选择
      if (data.region && data.region.adcode) {
        const { adcode } = data.region;

        // 处理仅粒度变化
        if (
          data.granularity !== this.currentProcessingGranularity &&
          adcode === this.currentProcessingAdcode
        ) {
          this.currentProcessingGranularity = data.granularity;
          this.handleGranularityChange(data.granularity);
        } else {
          // 默认绘制区域
          this.currentProcessingAdcode = adcode;
          this.drawRegion(adcode, {
            isInner: true,
            drawBoth: true,
          });
        }
      }
    },

    // 处理粒度变化，绘制内部轮廓
    handleGranularityChange(granularity) {
      // 获取ShapeDrawer组件
      const shapeDrawer = this.$refs.shapeDrawer;
      if (!shapeDrawer) return;

      this.$nextTick(() => {
        // 获取当前级别和选择的区域
        const selectedAdcode = shapeDrawer.selectedAdcode;
        // 先清除已有的轮廓
        this.clearType("polygon", false);

        // 绘制轮廓
        this.drawRegion(selectedAdcode, {
          isInner: !["self"].includes(granularity),
          drawBoth: !["self"].includes(granularity),
        });
      });
    },

    // 根据当前级别设置合适的粒度（选择第二级粒度）
    setAppropriateGranularity(level) {
      // 获取ShapeDrawer组件
      const shapeDrawer = this.$refs.shapeDrawer;
      if (!shapeDrawer) return;

      // 根据当前级别选择合适的粒度
      this.$nextTick(() => {
        switch (level) {
          case "country":
            // 国家级别，第二级粒度为province（省）
            shapeDrawer.granularity = "province";
            break;
          case "province":
            if (isMunicipality(shapeDrawer.selectedAdcode)) {
              shapeDrawer.granularity = "district";
              break;
            }
            if (shapeDrawer.selectedAdcode == "710000") {
              shapeDrawer.granularity = "self";
              break;
            }
            // 省级别，第二级粒度为city（市）
            shapeDrawer.granularity = "city";
            break;
          case "city":
            // 市级别，第二级粒度为district（区/县）
            shapeDrawer.granularity = "district";
            break;
          case "district":
            // 区县级别，保持为self（自身）
            shapeDrawer.granularity = "self";
            break;
        }
      });
    },
    // 下钻到当前粒度的行政区
    drillDown(properties) {
      // 如果已经在处理下钻/上钻操作，忽略此次请求
      if (this.isProcessingDrillOperation) {
        return;
      }

      // 标记正在处理下钻操作
      this.isProcessingDrillOperation = true;

      // 获取ShapeDrawer组件
      const shapeDrawer = this.$refs.shapeDrawer;
      if (!shapeDrawer) {
        this.isProcessingDrillOperation = false;
        return;
      }

      this.$nextTick(() => {
        // 获取当前级别和当前选择的粒度
        const currentLevel = shapeDrawer.currentLevel;
        const currentGranularity = shapeDrawer.granularity;

        // 检查是否允许下钻
        if (currentGranularity === "self") {
          this.isProcessingDrillOperation = false;
          return;
        }

        // 如果当前是区县级别，无法再下钻
        if (currentLevel === "district") {
          this.isProcessingDrillOperation = false;
          return;
        }

        const region = {
          name: properties.name,
          adcode: properties.adcode,
          level: properties.level,
          childrenNum: properties.childrenNum,
        };

        this.setAppropriateGranularity(region.level);
        // 更新当前选中区域，触发URL更新
        shapeDrawer.setCurrentRegion(region);

        // 完成下钻操作处理
        this.isProcessingDrillOperation = false;
      });
    },
    // 上钻到上一级
    drillUp() {
      // 如果已经在处理下钻/上钻操作，忽略此次请求
      if (this.isProcessingDrillOperation) {
        return;
      }

      // 标记正在处理上钻操作
      this.isProcessingDrillOperation = true;

      // 获取ShapeDrawer组件
      const shapeDrawer = this.$refs.shapeDrawer;
      if (!shapeDrawer) {
        this.isProcessingDrillOperation = false;
        return;
      }

      this.$nextTick(() => {
        // 获取当前级别和选择的区域
        const currentLevel = shapeDrawer.currentLevel;
        const selectedAdcode = shapeDrawer.selectedAdcode;

        // 如果当前是国家级别，无法再上钻
        if (currentLevel === "country") {
          this.isProcessingDrillOperation = false;
          return;
        }

        // 查找当前区域的上一级
        const allRegions = shapeDrawer.allRegions;
        let parentRegion = null;

        if (currentLevel === "province") {
          // 省级上钻到国家
          parentRegion = allRegions.find((item) => item.adcode === "100000");
        } else if (currentLevel === "city") {
          // 市级上钻到省级
          const currentRegion = allRegions.find(
            (item) => item.adcode === selectedAdcode
          );
          if (currentRegion && currentRegion.parent) {
            parentRegion = allRegions.find(
              (item) => item.adcode === currentRegion.parent
            );
          }
        } else if (currentLevel === "district") {
          // 区县上钻到市级
          const currentRegion = allRegions.find(
            (item) => item.adcode === selectedAdcode
          );
          if (currentRegion && currentRegion.parent) {
            parentRegion = allRegions.find(
              (item) => item.adcode === currentRegion.parent
            );
          }
        }

        if (parentRegion) {
          this.setAppropriateGranularity(parentRegion.level);
          // 更新当前选中区域，触发URL更新
          shapeDrawer.setCurrentRegion(parentRegion);
        }
        // 完成上钻操作处理
        this.isProcessingDrillOperation = false;
      });
    },

    // 处理多边形悬停事件
    handlePolygonHover(e, polygon) {
      // 如果抽屉未打开，不处理悬停事件
      if (!this.openShapeDrawer) return;

      // 获取ExtData中的信息
      const extData = polygon.getExtData();
      if (!extData) return;

      // 获取多边形的边界范围
      const bounds = polygon.getBounds();
      if (!bounds) return;

      // 获取多边形的路径点，查找最北的点（纬度最高的点）
      const path = polygon.getPath();
      if (!path || path.length === 0) return;

      // 找出纬度最高的点
      let northernmostPoint = path[0];

      path.forEach((point) => {
        if (point.getLat() > northernmostPoint.getLat()) {
          northernmostPoint = point;
        }
      });

      // 使用纬度最高的点的确切位置作为窗口位置
      const northPosition = new AMap.LngLat(
        northernmostPoint.getLng(),
        northernmostPoint.getLat()
      );

      const districtName = extData.properties.name;
      const districtAdcode = extData.properties.adcode;
      const districtLevel = extData.properties.level;

      const updatedContent = `
                <div style="padding: 8px; min-width: 150px;">
                    <div style="font-weight: bold; margin-bottom: 5px;">${districtName}</div>
                    <div>区域代码: ${districtAdcode}</div>
                    <div>行政级别: ${this.formatLevel(districtLevel)}</div>
                </div>
            `;

      // 使用自定义信息窗口显示更新内容
      this.showCustomInfoWindow(updatedContent, northPosition);
    },

    // 处理多边形点击事件（用于下钻）
    handlePolygonClick(e, properties) {
      // 如果抽屉未打开，不处理点击事件
      if (!this.openShapeDrawer) return;

      // 如果正在处理下钻/上钻操作，忽略此次点击
      if (this.isProcessingDrillOperation) return;

      // 尝试下钻
      this.drillDown(properties);
    },

    // 格式化行政级别显示
    formatLevel(level) {
      switch (level) {
        case "country":
          return "国家";
        case "province":
          return "省级";
        case "city":
          return "市级";
        case "district":
          return "区县级";
        default:
          return level;
      }
    },

    /**
     * 通用绘制区域方法
     * @param {string} adcode 区域编码
     * @param {Object} options 配置选项
     *   - fitView: 是否调整视野(默认true)
     *   - isInner: 是否是内部轮廓(默认false)
     *   - drawBoth: 是否同时绘制内外轮廓(默认false)
     */
    async drawRegion(adcode, options = {}) {
      const { fitView = true, isInner = false, drawBoth = false } = options;

      if (!adcode) return false;

      // 检查是否跳过绘制(仅当不是绘制内外轮廓时)
      if (!drawBoth && this.polygonList && this.polygonList.length > 0) {
        const hasOuterPolygon = this.polygonList.some(
          (item) =>
            item.polygon &&
            item.polygon.getExtData() &&
            !item.polygon.getExtData().isInner
        );
        if (hasOuterPolygon) return false;
      }

      try {
        this.clearType("polygon", false);
        // 先绘制外部轮廓(如果要求绘制内外轮廓)
        if (drawBoth) {
          await this.drawRegion(adcode, {
            fitView: false,
            isInner: false,
          });
        }

        // 构建API URL
        const urls = isInner
          ? this.shapeApiUrls
          : {
              jsonUrl: `/ali-datav/areas_v3/bound/${adcode}.json`,
              geojsonUrl: `/ali-datav/areas_v3/bound/geojson?code=${adcode}`,
            };

        // 获取数据
        const data = await Promise.any([
          fetch(urls.jsonUrl, {
            referrerPolicy: "no-referrer",
          }).then((r) => (r.ok ? r.json() : Promise.reject(r.status))),
          fetch(urls.geojsonUrl, {
            referrerPolicy: "no-referrer",
          }).then((r) => (r.ok ? r.json() : Promise.reject(r.status))),
        ]);

        // 绘制图形
        const strokeColor = this.shapeStrokeColor;
        const fillColor = this.shapeFillColor;
        let hasDrawnFeatures = false;

        const features =
          data.features ||
          (data.geometries
            ? data.geometries.map((g) => ({ geometry: g }))
            : []);

        for (const feature of features) {
          if (
            this.drawGeoJSONFeature(
              feature,
              strokeColor,
              fillColor,
              isInner || drawBoth
            )
          ) {
            hasDrawnFeatures = true;
          }
        }

        // 调整视野
        if (fitView && hasDrawnFeatures && this.polygonList.length > 0) {
          try {
            this.fitMapView(this.polygonList.map((item) => item.polygon));
          } catch (e) {
            window.console.error("设置地图视野失败:", e);
          }
        }

        // 更新UI
        if (this.$refs.shapeDrawer) {
          const region = this.$refs.shapeDrawer.allRegions.find(
            (item) => item.adcode === adcode
          );
          if (region) {
            this.$refs.shapeDrawer.selectedRegion = adcode;
          }
        }

        return hasDrawnFeatures;
      } catch (error) {
        this.$Message.error("获取行政区轮廓数据失败");
        return false;
      }
    },

    // 绘制GeoJSON要素
    drawGeoJSONFeature(feature, strokeColor, fillColor, isLine) {
      if (!feature.geometry) return false;

      const type = feature.geometry.type;
      const coordinates = feature.geometry.coordinates;
      const properties = {
        ...feature.properties,
        adcode: feature.properties.adcode.toString(),
      };

      let hasDrawnFeature = false;
      let lastPolygon = null;

      if (type === "Polygon") {
        coordinates.forEach((ring) => {
          const path = ring.map((coord) => new AMap.LngLat(coord[0], coord[1]));
          if (path && path.length > 0) {
            const polygon = this.createPolygon(
              path,
              strokeColor,
              fillColor,
              isLine,
              properties
            );
            hasDrawnFeature = true;
            lastPolygon = polygon;
          }
        });
      } else if (type === "MultiPolygon") {
        coordinates.forEach((polygon) => {
          polygon.forEach((ring) => {
            const path = ring.map(
              (coord) => new AMap.LngLat(coord[0], coord[1])
            );
            if (path && path.length > 0) {
              const polygon = this.createPolygon(
                path,
                strokeColor,
                fillColor,
                isLine,
                properties
              );
              hasDrawnFeature = true;
              lastPolygon = polygon;
            }
          });
        });
      }

      return hasDrawnFeature ? lastPolygon : false;
    },

    // 创建多边形
    createPolygon(path, strokeColor, fillColor, isLine, properties) {
      const polygon = new AMap.Polygon({
        path: path,
        strokeColor: strokeColor,
        strokeWeight: 2,
        fillColor: fillColor,
        fillOpacity: 0.05,
        // 如果是内部轮廓，使用虚线，增加间距
        strokeStyle: isLine ? "dashed" : "solid",
        // 自定义虚线样式，增加间距
        strokeDasharray: isLine ? [15, 10] : null, // 15像素实线，10像素空白
        // 内外轮廓使用相同的不透明度
        strokeOpacity: 1,
        bubble: true, // 让事件穿透到地图
      });

      // 保存是否是内部轮廓的信息，用于后续颜色更新
      polygon.setExtData({
        isInner: isLine,
        name: isLine ? "子区域" : "行政区轮廓", // 根据是否为内部轮廓设置不同的名称
        properties: properties,
      });

      // 添加鼠标悬停事件
      polygon.on("mouseover", (e) => {
        this.handlePolygonHover(e, polygon);
      });

      // 添加鼠标离开事件
      polygon.on("mouseout", () => {
        this.hideCustomInfoWindow();
      });

      // 添加点击事件，用于下钻
      polygon.on("click", (e) => {
        if (isLine) {
          this.handlePolygonClick(e, properties);
        }
      });

      this.gMap.add(polygon);

      // 保存到polygonList
      this.polygonList.push({
        polygon: polygon,
        name: isLine ? "子区域" : "行政区轮廓",
        id: polygon._amap_id,
        show: true,
      });

      return polygon;
    },
    // 调整地图视野
    fitMapView(overlays) {
      if (!overlays || overlays.length === 0) return;

      try {
        // 让地图适应所有覆盖物（默认居中）
        this.gMap.setFitView(overlays, false, [100, 100, 0, 0]);
      } catch (e) {
        window.console.error("调整地图视野失败", e);
      }
    },
    // 处理颜色更新
    handleColorUpdate(colors) {
      this.shapeStrokeColor = colors.strokeColor;
      this.shapeFillColor = colors.fillColor;

      // 如果有已绘制的轮廓，更新它们的颜色
      if (this.polygonList && this.polygonList.length > 0) {
        this.polygonList.forEach((item) => {
          if (item.polygon) {
            // 内部轮廓和外部轮廓使用相同的颜色，区别为线型（虚线/实线）
            const strokeColor = this.shapeStrokeColor;

            item.polygon.setOptions({
              strokeColor: strokeColor,
              fillColor: this.shapeFillColor,
            });
          }
        });
      }
    },

    // 处理文件上传
    handleFileUpload(fileData) {
      if (!fileData) {
        return;
      }

      // 显示加载提示
      this.$Spin.show();

      // 清除已有的轮廓
      this.clearType("polygon", false);

      try {
        // 处理不同格式的GeoJSON数据
        const data = fileData.content;

        if (data.features && data.features.length > 0) {
          // 标准GeoJSON格式
          data.features.forEach((feature) => {
            this.drawGeoJSONFeature(
              feature,
              this.shapeStrokeColor,
              this.shapeFillColor,
              false
            );
          });
        } else if (data.geometries && data.geometries.length > 0) {
          // 阿里云特殊格式
          data.geometries.forEach((geometry) => {
            this.drawGeoJSONFeature(
              { geometry },
              this.shapeStrokeColor,
              this.shapeFillColor,
              false
            );
          });
        } else {
          // 尝试作为单个几何体处理
          this.drawGeoJSONFeature(
            { geometry: data },
            this.shapeStrokeColor,
            this.shapeFillColor,
            false
          );
        }

        // 只有在成功绘制了轮廓后才设置适合的视野
        if (this.polygonList.length > 0) {
          try {
            // 缩放地图到合适的视野
            this.fitMapView(this.polygonList.map((item) => item.polygon));
          } catch (e) {
            window.console.error("设置地图视野失败:", e);
          }
        }

        this.$Spin.hide();
        this.$Message.success(`成功加载文件: ${fileData.fileName}`);
      } catch (error) {
        this.$Spin.hide();
        this.$Message.error("解析GeoJSON数据失败");
        window.console.error("解析GeoJSON数据失败:", error);
      }
    },
    // 创建自定义信息窗口
    createCustomInfoWindow() {
      // 创建自定义信息窗口DOM元素
      const customInfoWindow = document.createElement("div");
      customInfoWindow.className = "custom-info-window";
      customInfoWindow.style.display = "none";
      customInfoWindow.style.position = "absolute";
      customInfoWindow.style.backgroundColor = "white";
      customInfoWindow.style.padding = "10px";
      customInfoWindow.style.borderRadius = "4px";
      customInfoWindow.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.15)";
      customInfoWindow.style.zIndex = "999";
      customInfoWindow.style.minWidth = "150px";
      customInfoWindow.style.pointerEvents = "auto";

      // 创建小三角形元素
      const triangle = document.createElement("div");
      triangle.style.position = "absolute";
      triangle.style.bottom = "-8px";
      triangle.style.left = "50%";
      triangle.style.marginLeft = "-8px";
      triangle.style.width = "0";
      triangle.style.height = "0";
      triangle.style.borderLeft = "8px solid transparent";
      triangle.style.borderRight = "8px solid transparent";
      triangle.style.borderTop = "8px solid white";

      // 添加小三角形到信息窗口
      customInfoWindow.appendChild(triangle);

      // 添加到地图容器
      document.getElementById("container").appendChild(customInfoWindow);

      // 保存引用
      this.customInfoWindow = customInfoWindow;

      // 添加地图移动事件，移动时更新自定义窗口位置
      this.gMap.on("mapmove", () => {
        this.updateCustomInfoWindowPosition();
      });

      // 添加地图缩放事件，缩放时更新自定义窗口位置
      this.gMap.on("zoomchange", () => {
        this.updateCustomInfoWindowPosition();
      });
    },

    // 显示自定义信息窗口
    showCustomInfoWindow(content, position) {
      if (!this.customInfoWindow) return;

      // 设置内容区域 - 创建或获取内容容器
      let contentContainer = this.customInfoWindow.querySelector(
        ".info-content"
      );
      if (!contentContainer) {
        contentContainer = document.createElement("div");
        contentContainer.className = "info-content";
        // 在三角形之前插入内容容器
        if (this.customInfoWindow.firstChild) {
          this.customInfoWindow.insertBefore(
            contentContainer,
            this.customInfoWindow.firstChild
          );
        } else {
          this.customInfoWindow.appendChild(contentContainer);
        }
      }

      // 设置内容
      contentContainer.innerHTML = content;

      // 保存位置
      this.customInfoWindowPosition = position;

      // 先显示窗口，以便正确计算尺寸
      this.customInfoWindow.style.display = "block";
      this.customInfoWindowVisible = true;

      // 立即更新位置
      this.$nextTick(() => {
        this.updateCustomInfoWindowPosition();
      });
    },

    // 隐藏自定义信息窗口
    hideCustomInfoWindow() {
      if (!this.customInfoWindow) return;

      this.customInfoWindow.style.display = "none";
      this.customInfoWindowVisible = false;
    },

    // 更新自定义信息窗口位置
    updateCustomInfoWindowPosition() {
      if (
        !this.customInfoWindow ||
        !this.customInfoWindowPosition ||
        !this.customInfoWindowVisible
      )
        return;

      try {
        // 将经纬度转换为像素坐标
        const pixel = this.gMap.lngLatToContainer(
          this.customInfoWindowPosition
        );

        if (!pixel) return;

        // 设置位置，向上偏移，留出三角形的空间
        const width = this.customInfoWindow.offsetWidth || 150;
        const height = this.customInfoWindow.offsetHeight || 100;

        this.customInfoWindow.style.left = `${pixel.x - width / 2}px`;
        this.customInfoWindow.style.top = `${pixel.y - height - 10}px`;
      } catch (e) {
        window.console.error("更新信息窗口位置失败", e);
      }
    },
    handleDrawPolygon() {
      this.openDrawPolygon = !this.openDrawPolygon;

      // 如果关闭绘制模式，只清除绘制相关的事件监听
      if (!this.openDrawPolygon) {
        // 清除绘制相关的事件
        this.clearDrawingEvents();
        return;
      }

      // 显示绘制提示
      this.$Message.info({
        content: "点击添加顶点，双击结束绘制",
        duration: 5,
      });

      // 暂时关闭地图的双击缩放
      this.gMap.setStatus({ doubleClickZoom: false });

      let path = []; // 存储多边形顶点
      let polygon = null; // 多边形对象
      let tempLine = null; // 临时线段对象
      let mousePosition = null; // 当前鼠标位置
      let isDrawing = false; // 标记是否正在绘制
      let isDoubleClicking = false; // 标记是否正在处理双击

      // 更新多边形
      const updatePolygon = () => {
        // 移除旧的多边形
        if (polygon) {
          this.gMap.remove(polygon);
        }

        // 创建新多边形（至少两个点才创建）
        if (path.length >= 2) {
          // 验证所有点的有效性
          const validPoints = path
            .filter((point) => {
              return (
                !isNaN(point[0]) &&
                !isNaN(point[1]) &&
                point[0] !== null &&
                point[1] !== null
              );
            })
            .map(
              (point) => new AMap.LngLat(Number(point[0]), Number(point[1]))
            );

          if (validPoints.length < 2) return;

          // 使用 Polyline 而不是 Polygon，避免自动封闭
          polygon = new AMap.Polyline({
            path: validPoints,
            strokeColor: "#1890ff",
            strokeWeight: 2,
            strokeStyle: "solid",
            bubble: true, // 允许事件冒泡
          });

          this.gMap.add(polygon);
        }
      };

      // 更新临时线段
      const updateTempLine = () => {
        if (path.length === 0 || !mousePosition) return;

        // 移除旧的临时线段
        if (tempLine) {
          this.gMap.remove(tempLine);
        }

        // 创建临时线段路径
        let linePath = [];

        if (path.length === 1) {
          // 只有一个点时，只画从该点到鼠标位置的线
          linePath = [path[0], mousePosition];
        } else {
          // 多个点时，画两条线：最后一个点到鼠标位置，鼠标位置到第一个点
          linePath = [
            ...path.slice(), // 复制已有路径
            mousePosition, // 添加鼠标位置
            path[0], // 回到起始点形成闭合
          ];
        }

        tempLine = new AMap.Polyline({
          path: linePath,
          strokeColor: "#1890ff",
          strokeWeight: 2,
          strokeStyle: "dashed",
          strokeOpacity: 0.8,
          bubble: true, // 允许事件冒泡
        });

        this.gMap.add(tempLine);
      };

      // 完成绘制
      const finishDrawing = () => {
        if (path.length > 2) {
          try {
            // 验证所有点的有效性
            const validPath = path.filter((point) => {
              return (
                !isNaN(point[0]) &&
                !isNaN(point[1]) &&
                point[0] !== null &&
                point[1] !== null
              );
            });

            if (validPath.length < 3) {
              this.$Message.warning("有效点数不足，无法构成多边形");
              this.clearDrawingEvents();
              this.openDrawPolygon = false;
              return;
            }

            // 移除临时多边形
            if (polygon) {
              this.gMap.remove(polygon);
              polygon = null;
            }

            // 构造符合 addPolygonList 方法要求的数据格式
            const polygonData = {
              list: [
                {
                  name: "手绘多边形",
                  pointList: [
                    validPath.map((point) => ({
                      lng: Number(point[0]),
                      lat: Number(point[1]),
                      wgsLng: Number(point[0]),
                      wgsLat: Number(point[1]),
                    })),
                  ],
                },
              ],
            };

            // 使用 addPolygonList 添加多边形
            this.addPolygonList(polygonData, "#1890ff", false, false);

            // 获取刚添加的多边形
            const lastPolygon = this.polygonList[this.polygonList.length - 1]
              .polygon;

            // 设置为可编辑
            if (lastPolygon) {
              lastPolygon.setOptions({
                editable: true,
                fillOpacity: 0,
              });
            }

            this.$Message.success("多边形绘制完成");
          } catch (error) {
            window.console.error("绘制多边形出错:", error);
            this.$Message.error("绘制多边形出错，请重试");
          }
        } else {
          this.$Message.warning("至少需要3个点才能构成多边形");
        }

        // 清理绘制状态
        this.clearDrawingEvents();
        this.openDrawPolygon = false;
      };

      // 清理绘制相关的事件
      this.clearDrawingEvents = () => {
        // 移除临时线段
        if (tempLine) {
          this.gMap.remove(tempLine);
          tempLine = null;
        }

        // 移除临时多边形
        if (polygon) {
          this.gMap.remove(polygon);
          polygon = null;
        }

        // 重新启用地图的双击缩放
        this.gMap.setStatus({ doubleClickZoom: true });

        // 移除绘制相关的事件监听器
        if (this.drawClickListener) {
          this.gMap.off("click", this.drawClickListener);
          this.drawClickListener = null;
        }
        if (this.drawMoveListener) {
          this.gMap.off("mousemove", this.drawMoveListener);
          this.drawMoveListener = null;
        }
        if (this.drawDblClickListener) {
          this.gMap.off("dblclick", this.drawDblClickListener);
          this.drawDblClickListener = null;
        }

        // 重置绘制状态
        path = [];
        isDrawing = false;
        isDoubleClicking = false;
      };

      // 初始化鼠标位置
      this.drawMoveListener = (e) => {
        mousePosition = [e.lnglat.getLng(), e.lnglat.getLat()];
        if (isDrawing && path.length > 0) {
          updateTempLine();
        }
      };
      this.gMap.on("mousemove", this.drawMoveListener);

      // 点击地图开始/继续绘制
      this.drawClickListener = (e) => {
        // 如果正在处理双击，不处理点击
        if (isDoubleClicking) {
          return;
        }

        // 确保坐标有效
        if (
          e &&
          e.lnglat &&
          !isNaN(e.lnglat.getLng()) &&
          !isNaN(e.lnglat.getLat())
        ) {
          // 添加新顶点
          path.push([e.lnglat.getLng(), e.lnglat.getLat()]);

          // 第一次点击开始绘制
          if (!isDrawing) {
            isDrawing = true;
          }

          // 更新多边形
          updatePolygon();

          // 更新临时线段
          if (mousePosition) {
            updateTempLine();
          }
        } else {
          window.console.warn("无效的点击坐标");
        }
      };
      this.gMap.on("click", this.drawClickListener);

      // 双击结束绘制
      this.drawDblClickListener = () => {
        // 标记正在处理双击，防止点击事件被触发
        isDoubleClicking = true;

        // 如果正在绘制，完成绘制
        if (isDrawing) {
          // 不再移除最后一个点，保留所有点
          finishDrawing();
        }
      };
      this.gMap.on("dblclick", this.drawDblClickListener);
    },
    changeTheme(value) {
      this.gMap.setMapStyle("amap://styles/" + value);
      this.themeActive = value;
      this.themePoptipVisible = false;
    },
  },
};
</script>

<style scoped>
.amap-page-container,
.amap-demo {
  height: 100%;
}

.toolbar {
  position: absolute;
  bottom: 20px;
  right: 10px;
}

.show-version {
  position: absolute;
  right: 64px;
  bottom: 143px;
}
.theme-poptip {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: max-content;
  height: 150px;
  overflow-y: auto;
  position: absolute;
  top: 0;
  right: 60px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.theme-poptip::-webkit-scrollbar {
  width: 4px !important;
}
.theme-poptip::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #c9dfff;
}
.theme-poptip::-webkit-scrollbar-track {
  border-radius: 10px;
  background: transparent;
}
.theme-poptip::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0);
}
.theme-poptip div {
  padding: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-radius: 4px;
  text-align: center;
}
.theme-poptip div:hover {
  background-color: #f0f0f0;
}
.theme-poptip div.active {
  background-color: #f0f0f0;
  color: #000;
}
</style>
<style lang="less">
.my-ivu-drawer-wrap .ivu-drawer-body {
  padding: 0;
}

.my-ivu-drawer-wrap .ivu-drawer-header {
  background-color: #f8f8f9;
  border-bottom: 1px solid #e8eaec;
}

.drawer-subtitle {
  font-size: 12px;
  color: #808695;
  margin-left: 8px;
  font-weight: normal;
}
.shape-drawer-hidden {
  position: fixed;
  top: 50%;
  right: 30%;
  transform: translateY(-50%);
  z-index: 100;
  height: 40px;
  width: 30px;
  padding-left: 6px;
  background-color: #f8f8f9;
  cursor: pointer;
  border-radius: 50% 0 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: visible;
}
.personal-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0;
  }
  .ivu-modal-footer {
    display: none;
  }
  p {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 30px;
    color: #409efe;
    margin-bottom: 24px;
  }
  .form-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    .user-name {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 20px;
      color: #294558;
      display: flex;
      gap: 8px;
    }
    .key-id {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 20px;
      color: #294558;
      display: flex;
      gap: 8px;
      .key-input {
        min-width: 0;
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
        &-input {
          min-width: 0;
          flex: 1;
          display: flex;
        }
        .key-view-mode {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          .key-display {
            flex: 1;
            padding: 4px 8px;
            background-color: #f8f8f9;
            border: 1px solid #dcdee2;
            border-radius: 4px;
            font-size: 14px;
            color: #515a6e;
            word-break: break-all;
          }
          .edit-icon {
            cursor: pointer;
            color: #409efe;
            font-size: 16px;
            &:hover {
              color: #66b1ff;
            }
          }
        }
        .key-edit-mode {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          .key-check-btn {
            transition: all 0.3s ease;
          }
        }
      }
    }
    .key-tips {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
    }
    .form-footer {
      display: flex;
      justify-content: flex-end;
      .form-footer-wrap {
        display: flex;
        align-items: center;
        gap: 16px;
        .key-check-result {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 15px;
          &.success {
            color: rgba(36, 194, 52, 1);
          }
          &.fail {
            color: rgba(240, 40, 40, 1);
          }
        }
        .edit-buttons {
          display: flex;
          gap: 8px;
          .confirm-btn-disabled {
            background-color: #c5c8ce !important;
            border-color: #c5c8ce !important;
            color: #ffffff !important;
            cursor: not-allowed !important;
            transition: all 0.3s ease;
            &:hover {
              background-color: #c5c8ce !important;
              border-color: #c5c8ce !important;
              color: #ffffff !important;
            }
          }
        }
      }
    }
  }
}

// 校验按钮的过渡动画
.validate-btn-enter-active,
.validate-btn-leave-active {
  transition: all 0.3s ease;
}

.validate-btn-enter,
.validate-btn-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.validate-btn-enter-to,
.validate-btn-leave {
  opacity: 1;
  transform: scale(1);
}
</style>
