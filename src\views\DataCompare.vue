<template>
    <div>
        <Card>
            <p slot="title">数据库配置</p>
            <Form :model="tableForm" :label-width="80" inline>
                <FormItem label="主机">
                    <Input type="text" v-model="tableForm.ip" style="width: 220px"></Input>
                </FormItem>
                <FormItem label="端口">
                    <Input type="text" v-model="tableForm.port" style="width: 60px"></Input>
                </FormItem>
                <FormItem label="用户名">
                    <Input type="text" v-model="tableForm.user" style="width: 80px"></Input>
                </FormItem>
                <FormItem label="密码">
                    <Input type="password" v-model="tableForm.passwd" style="width: 120px"></Input>
                </FormItem>
                <FormItem label="库名">
                    <Input type="text" v-model="tableForm.db" style="width: 160px"></Input>
                </FormItem>
                <FormItem>
                    <Button type="primary" @click="loadTable">重新加载数据库</Button>
                </FormItem>
            </Form>
        </Card>
        <Card style="margin-top: 20px">
            <Spin size="large" fix v-if="loadDb"></Spin>
            <p slot="title">表数据差异对比</p>
            <Form :model="tableForm" :label-width="60" inline>
                <FormItem label="表A" prop="ip">
                    <Select v-model="tableForm.tableA" filterable clearable transfer style="width: 400px">
                        <Option v-for="item in tableList" :key="item" :value="item" style="float: left">{{item}}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem :label-width="60">
                    <Button type="primary" icon="ios-arrow-forward" @click="genTest"></Button>
                </FormItem>
                <FormItem label="表B" prop="ip">
                    <Select ref="selectB" v-model="tableForm.tableB" filterable clearable transfer style="width: 400px">
                        <Option v-for="item in tableList" :key="item" :value="item" style="float: left">{{item}}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem>
                    <Button type="primary" @click="loadCompareProcess">开始比对</Button>
                </FormItem>
            </Form>
            <Table no-data-text="无差异结果" :height="300" :loading="load" :columns="columns" :data="tableData"></Table>
        </Card>
    </div>
</template>

<script>
    import {post} from "../api";

    export default {
        name: "data-compare",
        data() {
            return {
                tableForm: {
                    ip: '*************',
                    port: 3312,
                    user: 'root',
                    passwd: 'mastercom168',
                    db: 'upos_city_js_nanjing',
                    tableA: 'tb_stat_grid_20190331 ',
                    tableB: 'tb_test_stat_grid_20190331 ',
                },
                tableList: [],
                tableData: [],
                columns: [],
                load: false,
                loadDb: false,
            }
        },
        mounted() {
            this.loadTable()
        },
        computed: {},
        methods: {
            genTest(){
                if(this.tableForm.tableA !== ''){
                    let testTable;
                    if(this.tableForm.tableA.indexOf("tb_test_") > -1){
                        testTable = this.tableForm.tableA.replace("tb_test_", "tb_")
                    }else{
                        testTable = this.tableForm.tableA.replace("tb_", "tb_test_")
                    }
                    this.tableForm.tableB = testTable
                }
            },
            loadTable() {
                this.loadDb = true
                post("/datacompare/getTableList/", this.tableForm)
                    .then(response => {
                        this.tableList = [];
                        response.data.map(o => {
                            this.tableList.push(o.table)
                        });
                        this.loadDb = false
                    })
            },
            loadCompareProcess() {
                this.load = true;
                post("/datacompare/compare/", this.tableForm)
                    .then(response => {
                        this.columns = [];
                        response.data.columns.forEach(p =>{
                            this.columns.push({
                                key: p,
                                title: p
                            })
                        });
                        this.tableData = [];
                        const length = response.data.data.length;
                        for (let i = 0; i < Math.min(500, length); i++) {
                            this.tableData.push(response.data.data[i])
                        }
                        this.load = false
                    })
            }
        }
    }
</script>

<style scoped>

</style>